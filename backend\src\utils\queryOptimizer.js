const mongoose = require('mongoose');
const logger = require('../config/logger');

/**
 * Database query optimization utilities
 */
class QueryOptimizer {
  constructor() {
    this.slowQueryThreshold = 100; // ms
    this.queryStats = new Map();
    this.enableProfiling = process.env.NODE_ENV === 'development';
  }

  /**
   * Optimize pagination queries
   */
  optimizePagination(query, page = 1, limit = 10, maxLimit = 100) {
    // Ensure reasonable limits
    const safeLimit = Math.min(Math.max(1, parseInt(limit)), maxLimit);
    const safePage = Math.max(1, parseInt(page));
    const skip = (safePage - 1) * safeLimit;

    return {
      skip,
      limit: safeLimit,
      page: safePage
    };
  }

  /**
   * Build optimized filter query
   */
  buildFilterQuery(filters = {}, allowedFields = []) {
    const query = {};

    Object.keys(filters).forEach(key => {
      if (!allowedFields.includes(key) || filters[key] === undefined || filters[key] === '') {
        return;
      }

      const value = filters[key];

      switch (key) {
        case 'search':
          // Text search optimization
          if (typeof value === 'string' && value.trim()) {
            query.$or = [
              { name: { $regex: value.trim(), $options: 'i' } },
              { description: { $regex: value.trim(), $options: 'i' } },
              { tags: { $in: [new RegExp(value.trim(), 'i')] } }
            ];
          }
          break;

        case 'category':
          query.category = value.toLowerCase();
          break;

        case 'status':
          query.status = value;
          break;

        case 'isActive':
        case 'isPaid':
        case 'isDiscontinued':
          query[key] = value === 'true' || value === true;
          break;

        case 'minPrice':
          query.price = { ...query.price, $gte: parseFloat(value) };
          break;

        case 'maxPrice':
          query.price = { ...query.price, $lte: parseFloat(value) };
          break;

        case 'minStock':
          query.stockQuantity = { ...query.stockQuantity, $gte: parseInt(value) };
          break;

        case 'maxStock':
          query.stockQuantity = { ...query.stockQuantity, $lte: parseInt(value) };
          break;

        case 'dateFrom':
          query.createdAt = { ...query.createdAt, $gte: new Date(value) };
          break;

        case 'dateTo':
          query.createdAt = { ...query.createdAt, $lte: new Date(value) };
          break;

        case 'customerName':
          query.customerName = { $regex: value, $options: 'i' };
          break;

        case 'lowStock':
          if (value === 'true' || value === true) {
            query.$expr = { $lte: ['$stockQuantity', '$minStockLevel'] };
          }
          break;

        case 'inStock':
          if (value === 'true' || value === true) {
            query.stockQuantity = { $gt: 0 };
          } else if (value === 'false' || value === false) {
            query.stockQuantity = 0;
          }
          break;

        case 'overdue':
          if (value === 'true' || value === true) {
            query.dueDate = { $lt: new Date() };
            query.isPaid = false;
          }
          break;

        default:
          // Direct field matching for other fields
          query[key] = value;
      }
    });

    return query;
  }

  /**
   * Build optimized sort query
   */
  buildSortQuery(sortBy = 'createdAt', sortOrder = 'desc', allowedFields = []) {
    const sort = {};
    
    if (allowedFields.includes(sortBy)) {
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
    } else {
      // Default sort
      sort.createdAt = -1;
    }

    return sort;
  }

  /**
   * Optimize field selection
   */
  optimizeFieldSelection(fields = [], excludeFields = ['__v']) {
    const select = {};
    
    if (fields.length > 0) {
      fields.forEach(field => {
        select[field] = 1;
      });
    } else {
      excludeFields.forEach(field => {
        select[field] = 0;
      });
    }

    return select;
  }

  /**
   * Execute optimized query with profiling
   */
  async executeQuery(model, query, options = {}) {
    const startTime = Date.now();
    const queryKey = this.generateQueryKey(model.modelName, query, options);

    try {
      let mongooseQuery = model.find(query);

      // Apply options
      if (options.select) {
        mongooseQuery = mongooseQuery.select(options.select);
      }

      if (options.sort) {
        mongooseQuery = mongooseQuery.sort(options.sort);
      }

      if (options.skip) {
        mongooseQuery = mongooseQuery.skip(options.skip);
      }

      if (options.limit) {
        mongooseQuery = mongooseQuery.limit(options.limit);
      }

      if (options.populate) {
        mongooseQuery = mongooseQuery.populate(options.populate);
      }

      // Execute query
      const result = await mongooseQuery.exec();
      const executionTime = Date.now() - startTime;

      // Log slow queries
      if (executionTime > this.slowQueryThreshold) {
        logger.warn('Slow query detected', {
          model: model.modelName,
          query: JSON.stringify(query),
          options: JSON.stringify(options),
          executionTime: `${executionTime}ms`
        });
      }

      // Update query statistics
      this.updateQueryStats(queryKey, executionTime);

      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      logger.error('Query execution failed', {
        model: model.modelName,
        query: JSON.stringify(query),
        options: JSON.stringify(options),
        executionTime: `${executionTime}ms`,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Execute optimized aggregation with profiling
   */
  async executeAggregation(model, pipeline, options = {}) {
    const startTime = Date.now();
    const queryKey = this.generateQueryKey(model.modelName, pipeline, { type: 'aggregation' });

    try {
      const result = await model.aggregate(pipeline, options);
      const executionTime = Date.now() - startTime;

      // Log slow aggregations
      if (executionTime > this.slowQueryThreshold) {
        logger.warn('Slow aggregation detected', {
          model: model.modelName,
          pipeline: JSON.stringify(pipeline),
          executionTime: `${executionTime}ms`
        });
      }

      // Update query statistics
      this.updateQueryStats(queryKey, executionTime);

      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      logger.error('Aggregation execution failed', {
        model: model.modelName,
        pipeline: JSON.stringify(pipeline),
        executionTime: `${executionTime}ms`,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Generate query key for statistics
   */
  generateQueryKey(modelName, query, options = {}) {
    const crypto = require('crypto');
    const queryString = JSON.stringify({ model: modelName, query, options });
    return crypto.createHash('md5').update(queryString).digest('hex');
  }

  /**
   * Update query statistics
   */
  updateQueryStats(queryKey, executionTime) {
    if (!this.enableProfiling) return;

    const stats = this.queryStats.get(queryKey) || {
      count: 0,
      totalTime: 0,
      minTime: Infinity,
      maxTime: 0,
      avgTime: 0
    };

    stats.count++;
    stats.totalTime += executionTime;
    stats.minTime = Math.min(stats.minTime, executionTime);
    stats.maxTime = Math.max(stats.maxTime, executionTime);
    stats.avgTime = Math.round(stats.totalTime / stats.count);

    this.queryStats.set(queryKey, stats);

    // Cleanup old stats (keep only last 1000 queries)
    if (this.queryStats.size > 1000) {
      const entries = Array.from(this.queryStats.entries());
      this.queryStats.clear();
      entries.slice(-500).forEach(([key, value]) => {
        this.queryStats.set(key, value);
      });
    }
  }

  /**
   * Get query performance statistics
   */
  getQueryStats() {
    const stats = Array.from(this.queryStats.entries()).map(([key, stats]) => ({
      queryKey: key,
      ...stats
    }));

    return {
      totalQueries: stats.reduce((sum, s) => sum + s.count, 0),
      slowQueries: stats.filter(s => s.avgTime > this.slowQueryThreshold).length,
      avgExecutionTime: stats.length > 0 ? 
        Math.round(stats.reduce((sum, s) => sum + s.avgTime, 0) / stats.length) : 0,
      topSlowQueries: stats
        .sort((a, b) => b.avgTime - a.avgTime)
        .slice(0, 10)
    };
  }

  /**
   * Optimize product queries
   */
  optimizeProductQuery(filters = {}, pagination = {}, sort = {}) {
    const allowedFilters = [
      'search', 'category', 'minPrice', 'maxPrice', 'inStock', 'lowStock',
      'isActive', 'isDiscontinued', 'dateFrom', 'dateTo'
    ];
    
    const allowedSortFields = [
      'name', 'price', 'stockQuantity', 'category', 'createdAt', 'updatedAt'
    ];

    const query = this.buildFilterQuery(filters, allowedFilters);
    const sortQuery = this.buildSortQuery(sort.sortBy, sort.sortOrder, allowedSortFields);
    const paginationOptions = this.optimizePagination(
      query, 
      pagination.page, 
      pagination.limit, 
      100
    );

    return {
      query,
      sort: sortQuery,
      ...paginationOptions,
      select: this.optimizeFieldSelection([], ['__v'])
    };
  }

  /**
   * Optimize debt queries
   */
  optimizeDebtQuery(filters = {}, pagination = {}, sort = {}) {
    const allowedFilters = [
      'customerName', 'status', 'isPaid', 'overdue', 'dateFrom', 'dateTo',
      'minAmount', 'maxAmount', 'priority'
    ];
    
    const allowedSortFields = [
      'customerName', 'totalAmount', 'dateOfDebt', 'dueDate', 'status', 'createdAt'
    ];

    const query = this.buildFilterQuery(filters, allowedFilters);
    const sortQuery = this.buildSortQuery(sort.sortBy, sort.sortOrder, allowedSortFields);
    const paginationOptions = this.optimizePagination(
      query, 
      pagination.page, 
      pagination.limit, 
      100
    );

    return {
      query,
      sort: sortQuery,
      ...paginationOptions,
      select: this.optimizeFieldSelection([], ['__v']),
      populate: {
        path: 'productId',
        select: 'name category price'
      }
    };
  }

  /**
   * Create database indexes
   */
  async createOptimalIndexes() {
    try {
      const db = mongoose.connection.db;
      
      // Product indexes
      await db.collection('products').createIndexes([
        { key: { name: 1 }, background: true },
        { key: { category: 1, isActive: 1 }, background: true },
        { key: { stockQuantity: 1, minStockLevel: 1 }, background: true },
        { key: { price: 1 }, background: true },
        { key: { createdAt: -1 }, background: true },
        { key: { name: 'text', description: 'text', tags: 'text' }, background: true }
      ]);

      // Debt indexes
      await db.collection('debts').createIndexes([
        { key: { customerName: 1, status: 1 }, background: true },
        { key: { isPaid: 1, dueDate: 1 }, background: true },
        { key: { dateOfDebt: -1 }, background: true },
        { key: { productId: 1 }, background: true },
        { key: { createdAt: -1 }, background: true }
      ]);

      logger.info('Database indexes created successfully');
    } catch (error) {
      logger.error('Failed to create database indexes:', error);
    }
  }

  /**
   * Analyze query performance
   */
  async analyzeQueryPerformance(model, query) {
    try {
      const explain = await model.find(query).explain('executionStats');
      
      const stats = {
        executionTimeMillis: explain.executionStats.executionTimeMillis,
        totalDocsExamined: explain.executionStats.totalDocsExamined,
        totalDocsReturned: explain.executionStats.totalDocsReturned,
        indexesUsed: explain.executionStats.executionStages?.indexName || 'COLLSCAN',
        isEfficient: explain.executionStats.totalDocsExamined <= explain.executionStats.totalDocsReturned * 2
      };

      if (!stats.isEfficient) {
        logger.warn('Inefficient query detected', {
          model: model.modelName,
          query: JSON.stringify(query),
          stats
        });
      }

      return stats;
    } catch (error) {
      logger.error('Query analysis failed:', error);
      return null;
    }
  }
}

// Create singleton instance
const queryOptimizer = new QueryOptimizer();

module.exports = queryOptimizer;
