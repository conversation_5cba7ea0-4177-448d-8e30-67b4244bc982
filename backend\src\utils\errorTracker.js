const logger = require('../config/logger');

/**
 * Error tracking and notification system
 */
class ErrorTracker {
  constructor() {
    this.errors = new Map(); // Store recent errors
    this.errorCounts = new Map(); // Track error frequency
    this.criticalErrors = [];
    this.maxStoredErrors = 1000;
    this.maxCriticalErrors = 100;
    
    // Cleanup old errors periodically
    setInterval(() => this.cleanup(), 60000); // Every minute
  }

  /**
   * Track an error
   */
  trackError(error, context = {}) {
    const errorKey = this.generateErrorKey(error);
    const timestamp = Date.now();
    
    const errorData = {
      id: this.generateErrorId(),
      message: error.message,
      stack: error.stack,
      name: error.name,
      code: error.code,
      timestamp,
      context,
      fingerprint: errorKey
    };

    // Store the error
    this.errors.set(errorData.id, errorData);
    
    // Update error count
    const currentCount = this.errorCounts.get(errorKey) || 0;
    this.errorCounts.set(errorKey, currentCount + 1);
    
    // Check if this is a critical error
    if (this.isCriticalError(error, currentCount + 1)) {
      this.handleCriticalError(errorData);
    }
    
    // Log the error
    logger.logError(error, context.req, {
      errorId: errorData.id,
      fingerprint: errorKey,
      count: currentCount + 1
    });
    
    return errorData.id;
  }

  /**
   * Generate a unique key for similar errors
   */
  generateErrorKey(error) {
    const message = error.message || 'Unknown error';
    const name = error.name || 'Error';
    const code = error.code || 'NO_CODE';
    
    // Create a fingerprint based on error characteristics
    const crypto = require('crypto');
    const fingerprint = `${name}:${code}:${message.substring(0, 100)}`;
    
    return crypto.createHash('md5').update(fingerprint).digest('hex');
  }

  /**
   * Generate unique error ID
   */
  generateErrorId() {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Check if error is critical
   */
  isCriticalError(error, count) {
    // Critical error conditions
    const criticalConditions = [
      // Database errors
      error.name === 'MongoNetworkError',
      error.name === 'MongoTimeoutError',
      error.name === 'MongoServerError',
      
      // Authentication errors (if frequent)
      error.code === 'AUTH_FAILED' && count > 10,
      
      // System errors
      error.code === 'ECONNREFUSED',
      error.code === 'ENOTFOUND',
      error.code === 'ETIMEDOUT',
      
      // Application errors
      error.name === 'ReferenceError',
      error.name === 'TypeError' && error.stack?.includes('Cannot read property'),
      
      // High frequency errors (same error > 50 times)
      count > 50,
      
      // Memory errors
      error.message?.includes('out of memory'),
      error.message?.includes('heap out of memory')
    ];
    
    return criticalConditions.some(condition => condition);
  }

  /**
   * Handle critical errors
   */
  handleCriticalError(errorData) {
    // Add to critical errors list
    this.criticalErrors.push(errorData);
    
    // Keep only recent critical errors
    if (this.criticalErrors.length > this.maxCriticalErrors) {
      this.criticalErrors = this.criticalErrors.slice(-this.maxCriticalErrors);
    }
    
    // Log critical error
    logger.error('CRITICAL ERROR DETECTED', {
      errorId: errorData.id,
      message: errorData.message,
      fingerprint: errorData.fingerprint,
      context: errorData.context
    });
    
    // Send notifications (if configured)
    this.sendCriticalErrorNotification(errorData);
  }

  /**
   * Send critical error notifications
   */
  async sendCriticalErrorNotification(errorData) {
    try {
      // Email notification (if configured)
      if (process.env.SMTP_HOST && process.env.CRITICAL_ERROR_EMAIL) {
        await this.sendEmailNotification(errorData);
      }
      
      // Slack notification (if configured)
      if (process.env.SLACK_WEBHOOK_URL) {
        await this.sendSlackNotification(errorData);
      }
      
      // Custom webhook (if configured)
      if (process.env.ERROR_WEBHOOK_URL) {
        await this.sendWebhookNotification(errorData);
      }
    } catch (notificationError) {
      logger.error('Failed to send error notification:', notificationError);
    }
  }

  /**
   * Send email notification
   */
  async sendEmailNotification(errorData) {
    const nodemailer = require('nodemailer');
    
    const transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT || 587,
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
    
    const mailOptions = {
      from: process.env.FROM_EMAIL,
      to: process.env.CRITICAL_ERROR_EMAIL,
      subject: `🚨 Critical Error in ${process.env.APP_NAME}`,
      html: `
        <h2>Critical Error Alert</h2>
        <p><strong>Error ID:</strong> ${errorData.id}</p>
        <p><strong>Time:</strong> ${new Date(errorData.timestamp).toISOString()}</p>
        <p><strong>Message:</strong> ${errorData.message}</p>
        <p><strong>Environment:</strong> ${process.env.NODE_ENV}</p>
        
        ${errorData.context.req ? `
        <h3>Request Details</h3>
        <p><strong>Method:</strong> ${errorData.context.req.method}</p>
        <p><strong>URL:</strong> ${errorData.context.req.originalUrl}</p>
        <p><strong>IP:</strong> ${errorData.context.req.ip}</p>
        ` : ''}
        
        <h3>Stack Trace</h3>
        <pre>${errorData.stack}</pre>
      `
    };
    
    await transporter.sendMail(mailOptions);
    logger.info('Critical error email notification sent', { errorId: errorData.id });
  }

  /**
   * Send Slack notification
   */
  async sendSlackNotification(errorData) {
    const axios = require('axios');
    
    const payload = {
      text: `🚨 Critical Error in ${process.env.APP_NAME}`,
      attachments: [
        {
          color: 'danger',
          fields: [
            {
              title: 'Error ID',
              value: errorData.id,
              short: true
            },
            {
              title: 'Environment',
              value: process.env.NODE_ENV,
              short: true
            },
            {
              title: 'Message',
              value: errorData.message,
              short: false
            },
            {
              title: 'Time',
              value: new Date(errorData.timestamp).toISOString(),
              short: true
            }
          ]
        }
      ]
    };
    
    await axios.post(process.env.SLACK_WEBHOOK_URL, payload);
    logger.info('Critical error Slack notification sent', { errorId: errorData.id });
  }

  /**
   * Send webhook notification
   */
  async sendWebhookNotification(errorData) {
    const axios = require('axios');
    
    await axios.post(process.env.ERROR_WEBHOOK_URL, {
      type: 'critical_error',
      data: errorData,
      environment: process.env.NODE_ENV,
      service: process.env.APP_NAME
    });
    
    logger.info('Critical error webhook notification sent', { errorId: errorData.id });
  }

  /**
   * Get error statistics
   */
  getErrorStats() {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    const oneDay = 24 * oneHour;
    
    const recentErrors = Array.from(this.errors.values());
    
    const stats = {
      total: recentErrors.length,
      lastHour: recentErrors.filter(e => now - e.timestamp < oneHour).length,
      lastDay: recentErrors.filter(e => now - e.timestamp < oneDay).length,
      critical: this.criticalErrors.length,
      topErrors: this.getTopErrors(10),
      errorRate: this.calculateErrorRate()
    };
    
    return stats;
  }

  /**
   * Get top errors by frequency
   */
  getTopErrors(limit = 10) {
    return Array.from(this.errorCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([fingerprint, count]) => {
        const recentError = Array.from(this.errors.values())
          .find(e => e.fingerprint === fingerprint);
        
        return {
          fingerprint,
          count,
          message: recentError?.message || 'Unknown',
          lastSeen: recentError?.timestamp || 0
        };
      });
  }

  /**
   * Calculate error rate
   */
  calculateErrorRate() {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    
    const recentErrors = Array.from(this.errors.values())
      .filter(e => now - e.timestamp < oneHour);
    
    return {
      errorsPerHour: recentErrors.length,
      errorsPerMinute: Math.round(recentErrors.length / 60 * 100) / 100
    };
  }

  /**
   * Cleanup old errors
   */
  cleanup() {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    
    // Remove old errors
    for (const [id, error] of this.errors.entries()) {
      if (now - error.timestamp > maxAge) {
        this.errors.delete(id);
      }
    }
    
    // Remove old critical errors
    this.criticalErrors = this.criticalErrors.filter(
      error => now - error.timestamp < maxAge
    );
    
    // Limit stored errors
    if (this.errors.size > this.maxStoredErrors) {
      const sortedErrors = Array.from(this.errors.entries())
        .sort((a, b) => b[1].timestamp - a[1].timestamp);
      
      this.errors.clear();
      sortedErrors.slice(0, this.maxStoredErrors).forEach(([id, error]) => {
        this.errors.set(id, error);
      });
    }
    
    logger.debug('Error tracker cleanup completed', {
      totalErrors: this.errors.size,
      criticalErrors: this.criticalErrors.length
    });
  }

  /**
   * Get error by ID
   */
  getError(errorId) {
    return this.errors.get(errorId);
  }

  /**
   * Get all critical errors
   */
  getCriticalErrors() {
    return this.criticalErrors;
  }

  /**
   * Clear all errors (for testing)
   */
  clear() {
    this.errors.clear();
    this.errorCounts.clear();
    this.criticalErrors = [];
  }
}

// Create singleton instance
const errorTracker = new ErrorTracker();

module.exports = errorTracker;
