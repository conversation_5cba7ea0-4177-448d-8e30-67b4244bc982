const mongoose = require('mongoose');
const logger = require('../config/logger');

/**
 * System monitoring utilities
 */
class SystemMonitor {
  constructor() {
    this.startTime = Date.now();
    this.requestCount = 0;
    this.errorCount = 0;
    this.responseTimeSum = 0;
    this.memoryUsage = [];
    this.cpuUsage = [];
    
    // Start monitoring intervals
    this.startMemoryMonitoring();
    this.startHealthChecks();
  }

  /**
   * Get system uptime in seconds
   */
  getUptime() {
    return Math.floor((Date.now() - this.startTime) / 1000);
  }

  /**
   * Get memory usage statistics
   */
  getMemoryStats() {
    const usage = process.memoryUsage();
    return {
      rss: Math.round(usage.rss / 1024 / 1024 * 100) / 100, // MB
      heapTotal: Math.round(usage.heapTotal / 1024 / 1024 * 100) / 100, // MB
      heapUsed: Math.round(usage.heapUsed / 1024 / 1024 * 100) / 100, // MB
      external: Math.round(usage.external / 1024 / 1024 * 100) / 100, // MB
      arrayBuffers: Math.round(usage.arrayBuffers / 1024 / 1024 * 100) / 100 // MB
    };
  }

  /**
   * Get CPU usage (approximation)
   */
  getCpuStats() {
    const usage = process.cpuUsage();
    return {
      user: usage.user / 1000000, // Convert to seconds
      system: usage.system / 1000000 // Convert to seconds
    };
  }

  /**
   * Get database connection status
   */
  async getDatabaseStatus() {
    try {
      const state = mongoose.connection.readyState;
      const states = {
        0: 'disconnected',
        1: 'connected',
        2: 'connecting',
        3: 'disconnecting'
      };

      const status = {
        status: states[state] || 'unknown',
        host: mongoose.connection.host,
        port: mongoose.connection.port,
        name: mongoose.connection.name
      };

      if (state === 1) {
        // Get database stats if connected
        const admin = mongoose.connection.db.admin();
        const serverStatus = await admin.serverStatus();
        
        status.version = serverStatus.version;
        status.uptime = serverStatus.uptime;
        status.connections = serverStatus.connections;
      }

      return status;
    } catch (error) {
      logger.error('Failed to get database status:', error);
      return {
        status: 'error',
        error: error.message
      };
    }
  }

  /**
   * Get API performance metrics
   */
  getApiMetrics() {
    return {
      totalRequests: this.requestCount,
      totalErrors: this.errorCount,
      averageResponseTime: this.requestCount > 0 ? 
        Math.round(this.responseTimeSum / this.requestCount * 100) / 100 : 0,
      errorRate: this.requestCount > 0 ? 
        Math.round(this.errorCount / this.requestCount * 10000) / 100 : 0 // Percentage
    };
  }

  /**
   * Record API request
   */
  recordRequest(responseTime, isError = false) {
    this.requestCount++;
    this.responseTimeSum += responseTime;
    
    if (isError) {
      this.errorCount++;
    }
  }

  /**
   * Get comprehensive health status
   */
  async getHealthStatus() {
    const memoryStats = this.getMemoryStats();
    const cpuStats = this.getCpuStats();
    const databaseStatus = await this.getDatabaseStatus();
    const apiMetrics = this.getApiMetrics();

    // Determine overall health
    let status = 'healthy';
    const issues = [];

    // Check memory usage (warn if > 80% of heap)
    const memoryUsagePercent = (memoryStats.heapUsed / memoryStats.heapTotal) * 100;
    if (memoryUsagePercent > 90) {
      status = 'critical';
      issues.push('High memory usage');
    } else if (memoryUsagePercent > 80) {
      status = 'warning';
      issues.push('Elevated memory usage');
    }

    // Check database connection
    if (databaseStatus.status !== 'connected') {
      status = 'critical';
      issues.push('Database connection issue');
    }

    // Check error rate (warn if > 5%)
    if (apiMetrics.errorRate > 10) {
      status = 'critical';
      issues.push('High error rate');
    } else if (apiMetrics.errorRate > 5) {
      status = 'warning';
      issues.push('Elevated error rate');
    }

    return {
      status,
      timestamp: new Date().toISOString(),
      uptime: this.getUptime(),
      version: process.env.APP_VERSION || '2.0.0',
      environment: process.env.NODE_ENV || 'development',
      memory: memoryStats,
      cpu: cpuStats,
      database: databaseStatus,
      api: apiMetrics,
      issues: issues.length > 0 ? issues : null
    };
  }

  /**
   * Start memory monitoring
   */
  startMemoryMonitoring() {
    setInterval(() => {
      const memStats = this.getMemoryStats();
      this.memoryUsage.push({
        timestamp: Date.now(),
        ...memStats
      });

      // Keep only last 100 entries (about 16 minutes at 10s intervals)
      if (this.memoryUsage.length > 100) {
        this.memoryUsage = this.memoryUsage.slice(-100);
      }

      // Log warning if memory usage is high
      const memoryUsagePercent = (memStats.heapUsed / memStats.heapTotal) * 100;
      if (memoryUsagePercent > 85) {
        logger.warn('High memory usage detected', {
          heapUsed: memStats.heapUsed,
          heapTotal: memStats.heapTotal,
          usagePercent: Math.round(memoryUsagePercent * 100) / 100
        });
      }
    }, 10000); // Every 10 seconds
  }

  /**
   * Start periodic health checks
   */
  startHealthChecks() {
    setInterval(async () => {
      try {
        const health = await this.getHealthStatus();
        
        if (health.status === 'critical') {
          logger.error('System health check failed', health);
        } else if (health.status === 'warning') {
          logger.warn('System health warning', health);
        } else {
          logger.debug('System health check passed', {
            status: health.status,
            uptime: health.uptime,
            memoryUsed: health.memory.heapUsed,
            errorRate: health.api.errorRate
          });
        }
      } catch (error) {
        logger.error('Health check failed:', error);
      }
    }, 60000); // Every minute
  }

  /**
   * Get memory usage history
   */
  getMemoryHistory() {
    return this.memoryUsage;
  }

  /**
   * Reset metrics
   */
  resetMetrics() {
    this.requestCount = 0;
    this.errorCount = 0;
    this.responseTimeSum = 0;
    this.memoryUsage = [];
    logger.info('System metrics reset');
  }

  /**
   * Get disk usage (if available)
   */
  async getDiskUsage() {
    try {
      const fs = require('fs').promises;
      const stats = await fs.stat(process.cwd());
      
      return {
        available: true,
        path: process.cwd(),
        // Note: Getting actual disk usage requires platform-specific code
        // This is a simplified version
        lastModified: stats.mtime
      };
    } catch (error) {
      return {
        available: false,
        error: error.message
      };
    }
  }

  /**
   * Generate system report
   */
  async generateReport() {
    const health = await this.getHealthStatus();
    const memoryHistory = this.getMemoryHistory();
    const diskUsage = await this.getDiskUsage();

    return {
      ...health,
      memoryHistory,
      diskUsage,
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      pid: process.pid
    };
  }
}

// Create singleton instance
const monitor = new SystemMonitor();

/**
 * Express middleware for monitoring requests
 */
const monitoringMiddleware = (req, res, next) => {
  const startTime = Date.now();
  
  // Override res.end to capture response time
  const originalEnd = res.end;
  res.end = function(...args) {
    const responseTime = Date.now() - startTime;
    const isError = res.statusCode >= 400;
    
    monitor.recordRequest(responseTime, isError);
    
    // Log slow requests
    if (responseTime > 1000) {
      logger.warn('Slow request detected', {
        method: req.method,
        url: req.originalUrl,
        responseTime: `${responseTime}ms`,
        statusCode: res.statusCode
      });
    }
    
    originalEnd.apply(this, args);
  };
  
  next();
};

module.exports = {
  monitor,
  monitoringMiddleware
};
