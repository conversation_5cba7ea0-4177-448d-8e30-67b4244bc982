const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const logger = require('../src/config/logger');

let mongoServer;

/**
 * Test Database Setup
 * Sets up in-memory MongoDB for testing
 */

// Setup before all tests
const setupTestDB = async () => {
  try {
    // Start in-memory MongoDB instance
    mongoServer = await MongoMemoryServer.create({
      instance: {
        dbName: 'sari-sari-test',
        port: 27018
      }
    });
    
    const mongoUri = mongoServer.getUri();
    
    // Connect to the in-memory database
    await mongoose.connect(mongoUri, {
      maxPoolSize: 5,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000
    });
    
    logger.info('Test database connected');
  } catch (error) {
    logger.error('Test database setup failed:', error);
    throw error;
  }
};

// Cleanup after all tests
const teardownTestDB = async () => {
  try {
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
    }
    
    if (mongoServer) {
      await mongoServer.stop();
    }
    
    logger.info('Test database cleaned up');
  } catch (error) {
    logger.error('Test database cleanup failed:', error);
    throw error;
  }
};

// Clear all collections before each test
const clearTestDB = async () => {
  try {
    const collections = mongoose.connection.collections;
    
    for (const key in collections) {
      const collection = collections[key];
      await collection.deleteMany({});
    }
  } catch (error) {
    logger.error('Test database clear failed:', error);
    throw error;
  }
};

// Create test data helpers
const createTestProduct = (overrides = {}) => {
  return {
    name: 'Test Product',
    description: 'Test product description',
    netWeight: '100g',
    price: 25.00,
    costPrice: 20.00,
    stockQuantity: 50,
    minStockLevel: 10,
    category: 'snacks',
    barcode: '1234567890123',
    sku: 'TEST-PROD-001',
    tags: ['test', 'sample'],
    isActive: true,
    isDiscontinued: false,
    ...overrides
  };
};

const createTestDebt = (productId, overrides = {}) => {
  return {
    customerName: 'Test Customer',
    customerPhone: '+63-************',
    customerEmail: '<EMAIL>',
    productName: 'Test Product',
    productId: productId,
    priceAtTimeOfDebt: 25.00,
    quantity: 2,
    dateOfDebt: new Date(),
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    isPaid: false,
    notes: 'Test debt record',
    priority: 'medium',
    tags: ['test'],
    createdBy: null,
    ...overrides
  };
};

const createTestUser = (overrides = {}) => {
  return {
    firstName: 'Test',
    lastName: 'User',
    username: 'testuser',
    email: '<EMAIL>',
    password: 'TestPassword123!',
    role: 'user',
    isActive: true,
    isEmailVerified: true,
    ...overrides
  };
};

// Test utilities
const generateRandomString = (length = 10) => {
  return Math.random().toString(36).substring(2, length + 2);
};

const generateRandomEmail = () => {
  return `test${generateRandomString(5)}@example.com`;
};

const generateRandomPhone = () => {
  return `+63-9${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`;
};

const waitFor = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Mock data generators
const generateMockProducts = (count = 5) => {
  const categories = ['snacks', 'beverages', 'canned goods', 'personal care', 'household items'];
  const products = [];
  
  for (let i = 0; i < count; i++) {
    products.push(createTestProduct({
      name: `Test Product ${i + 1}`,
      price: Math.floor(Math.random() * 100) + 10,
      stockQuantity: Math.floor(Math.random() * 100) + 1,
      category: categories[Math.floor(Math.random() * categories.length)],
      barcode: `123456789012${i}`,
      sku: `TEST-PROD-${String(i + 1).padStart(3, '0')}`
    }));
  }
  
  return products;
};

const generateMockDebts = (productIds, count = 5) => {
  const customers = ['Maria Santos', 'Juan Dela Cruz', 'Ana Rodriguez', 'Pedro Gonzales', 'Lisa Garcia'];
  const debts = [];
  
  for (let i = 0; i < count; i++) {
    const productId = productIds[Math.floor(Math.random() * productIds.length)];
    debts.push(createTestDebt(productId, {
      customerName: customers[Math.floor(Math.random() * customers.length)],
      quantity: Math.floor(Math.random() * 5) + 1,
      isPaid: Math.random() > 0.5,
      priority: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)]
    }));
  }
  
  return debts;
};

// Error simulation helpers
const simulateNetworkError = () => {
  const error = new Error('Network Error');
  error.code = 'NETWORK_ERROR';
  return error;
};

const simulateDatabaseError = () => {
  const error = new Error('Database Error');
  error.name = 'MongoError';
  return error;
};

const simulateValidationError = (field, message) => {
  const error = new Error('Validation Error');
  error.name = 'ValidationError';
  error.errors = {
    [field]: { message }
  };
  return error;
};

module.exports = {
  setupTestDB,
  teardownTestDB,
  clearTestDB,
  createTestProduct,
  createTestDebt,
  createTestUser,
  generateRandomString,
  generateRandomEmail,
  generateRandomPhone,
  waitFor,
  generateMockProducts,
  generateMockDebts,
  simulateNetworkError,
  simulateDatabaseError,
  simulateValidationError
};
