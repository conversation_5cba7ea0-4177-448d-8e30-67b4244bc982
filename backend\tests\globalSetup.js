const { MongoMemoryServer } = require('mongodb-memory-server');

module.exports = async () => {
  // Set test environment
  process.env.NODE_ENV = 'test';
  process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests
  
  // Start MongoDB Memory Server
  const mongoServer = await MongoMemoryServer.create({
    instance: {
      dbName: 'sari-sari-test-global',
      port: 27019
    }
  });
  
  // Store the server instance globally
  global.__MONGOSERVER__ = mongoServer;
  
  // Set the test database URI
  process.env.MONGODB_TEST_URI = mongoServer.getUri();
  
  console.log('Global test setup completed');
};
