const request = require('supertest');
const app = require('../../../server');
const Product = require('../../../models/Product');
const { 
  setupTestDB, 
  teardownTestDB, 
  clearTestDB, 
  createTestProduct,
  generateMockProducts 
} = require('../../setup');

describe('Product Routes', () => {
  beforeAll(async () => {
    await setupTestDB();
  });

  afterAll(async () => {
    await teardownTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();
  });

  describe('GET /api/products', () => {
    beforeEach(async () => {
      const products = generateMockProducts(10);
      await Product.insertMany(products);
    });

    it('should get all products with default pagination', async () => {
      const response = await request(app)
        .get('/api/products')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(10);
      expect(response.body.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 10,
        pages: 1,
        hasNext: false,
        hasPrev: false
      });
    });

    it('should filter products by category', async () => {
      const response = await request(app)
        .get('/api/products?category=snacks')
        .expect(200);

      expect(response.body.success).toBe(true);
      response.body.data.forEach(product => {
        expect(product.category).toBe('snacks');
      });
    });

    it('should search products by name', async () => {
      const response = await request(app)
        .get('/api/products?search=Test Product 1')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
    });

    it('should paginate results correctly', async () => {
      const response = await request(app)
        .get('/api/products?page=2&limit=5')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(5);
      expect(response.body.pagination.page).toBe(2);
      expect(response.body.pagination.limit).toBe(5);
    });

    it('should sort products correctly', async () => {
      const response = await request(app)
        .get('/api/products?sortBy=price&sortOrder=asc')
        .expect(200);

      expect(response.body.success).toBe(true);
      const prices = response.body.data.map(p => p.price);
      const sortedPrices = [...prices].sort((a, b) => a - b);
      expect(prices).toEqual(sortedPrices);
    });

    it('should filter by stock status', async () => {
      // Create out of stock product
      await Product.create(createTestProduct({ 
        name: 'Out of Stock Product',
        stockQuantity: 0 
      }));

      const response = await request(app)
        .get('/api/products?inStock=false')
        .expect(200);

      expect(response.body.success).toBe(true);
      response.body.data.forEach(product => {
        expect(product.stockQuantity).toBe(0);
      });
    });

    it('should filter by price range', async () => {
      const response = await request(app)
        .get('/api/products?minPrice=20&maxPrice=50')
        .expect(200);

      expect(response.body.success).toBe(true);
      response.body.data.forEach(product => {
        expect(product.price).toBeGreaterThanOrEqual(20);
        expect(product.price).toBeLessThanOrEqual(50);
      });
    });

    it('should return validation error for invalid parameters', async () => {
      const response = await request(app)
        .get('/api/products?page=0')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Validation errors');
    });
  });

  describe('GET /api/products/:id', () => {
    let product;

    beforeEach(async () => {
      product = await Product.create(createTestProduct());
    });

    it('should get a single product by ID', async () => {
      const response = await request(app)
        .get(`/api/products/${product._id}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data._id).toBe(product._id.toString());
      expect(response.body.data.name).toBe(product.name);
    });

    it('should return 404 for non-existent product', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const response = await request(app)
        .get(`/api/products/${fakeId}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Product not found');
    });

    it('should return 400 for invalid product ID', async () => {
      const response = await request(app)
        .get('/api/products/invalid-id')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Validation errors');
    });

    it('should return 404 for inactive product', async () => {
      product.isActive = false;
      await product.save();

      const response = await request(app)
        .get(`/api/products/${product._id}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Product is no longer available');
    });
  });

  describe('POST /api/products', () => {
    it('should create a new product', async () => {
      const productData = createTestProduct();

      const response = await request(app)
        .post('/api/products')
        .send(productData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe(productData.name);
      expect(response.body.data.price).toBe(productData.price);

      // Verify product was saved to database
      const savedProduct = await Product.findById(response.body.data._id);
      expect(savedProduct).toBeTruthy();
    });

    it('should return validation error for missing required fields', async () => {
      const response = await request(app)
        .post('/api/products')
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Validation errors');
      expect(response.body.errors).toBeDefined();
    });

    it('should return validation error for invalid price', async () => {
      const productData = createTestProduct({ price: -10 });

      const response = await request(app)
        .post('/api/products')
        .send(productData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Validation errors');
    });

    it('should return validation error for invalid category', async () => {
      const productData = createTestProduct({ category: 'invalid-category' });

      const response = await request(app)
        .post('/api/products')
        .send(productData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Validation errors');
    });

    it('should prevent duplicate product names', async () => {
      const productData = createTestProduct();
      await Product.create(productData);

      const duplicateData = createTestProduct({
        barcode: '9876543210987',
        sku: 'DIFFERENT-SKU'
      });

      const response = await request(app)
        .post('/api/products')
        .send(duplicateData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('A product with this name already exists');
    });

    it('should prevent duplicate barcodes', async () => {
      const productData = createTestProduct();
      await Product.create(productData);

      const duplicateData = createTestProduct({
        name: 'Different Product Name',
        sku: 'DIFFERENT-SKU'
      });

      const response = await request(app)
        .post('/api/products')
        .send(duplicateData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('A product with this barcode already exists');
    });

    it('should prevent duplicate SKUs', async () => {
      const productData = createTestProduct();
      await Product.create(productData);

      const duplicateData = createTestProduct({
        name: 'Different Product Name',
        barcode: '9876543210987'
      });

      const response = await request(app)
        .post('/api/products')
        .send(duplicateData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('A product with this SKU already exists');
    });
  });

  describe('PUT /api/products/:id', () => {
    let product;

    beforeEach(async () => {
      product = await Product.create(createTestProduct());
    });

    it('should update a product', async () => {
      const updateData = {
        name: 'Updated Product Name',
        price: 35.00,
        stockQuantity: 75
      };

      const response = await request(app)
        .put(`/api/products/${product._id}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe(updateData.name);
      expect(response.body.data.price).toBe(updateData.price);
      expect(response.body.data.stockQuantity).toBe(updateData.stockQuantity);

      // Verify update in database
      const updatedProduct = await Product.findById(product._id);
      expect(updatedProduct.name).toBe(updateData.name);
    });

    it('should return 404 for non-existent product', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const response = await request(app)
        .put(`/api/products/${fakeId}`)
        .send({ name: 'Updated Name' })
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Product not found');
    });

    it('should return validation error for invalid data', async () => {
      const response = await request(app)
        .put(`/api/products/${product._id}`)
        .send({ price: -10 })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Validation errors');
    });
  });

  describe('DELETE /api/products/:id', () => {
    let product;

    beforeEach(async () => {
      product = await Product.create(createTestProduct());
    });

    it('should soft delete a product', async () => {
      const response = await request(app)
        .delete(`/api/products/${product._id}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Product deleted successfully');

      // Verify product is marked as inactive
      const deletedProduct = await Product.findById(product._id);
      expect(deletedProduct.isActive).toBe(false);
    });

    it('should return 404 for non-existent product', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const response = await request(app)
        .delete(`/api/products/${fakeId}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Product not found');
    });

    it('should return error when trying to delete already deleted product', async () => {
      product.isActive = false;
      await product.save();

      const response = await request(app)
        .delete(`/api/products/${product._id}`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Product is already deleted');
    });
  });
});
