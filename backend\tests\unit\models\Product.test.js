const mongoose = require('mongoose');
const Product = require('../../../models/Product');
const { 
  setupTestDB, 
  teardownTestDB, 
  clearTestDB, 
  createTestProduct,
  generateRandomString 
} = require('../../setup');

describe('Product Model', () => {
  beforeAll(async () => {
    await setupTestDB();
  });

  afterAll(async () => {
    await teardownTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();
  });

  describe('Product Creation', () => {
    it('should create a valid product', async () => {
      const productData = createTestProduct();
      const product = new Product(productData);
      const savedProduct = await product.save();

      expect(savedProduct._id).toBeDefined();
      expect(savedProduct.name).toBe(productData.name);
      expect(savedProduct.price).toBe(productData.price);
      expect(savedProduct.stockQuantity).toBe(productData.stockQuantity);
      expect(savedProduct.category).toBe(productData.category);
      expect(savedProduct.isActive).toBe(true);
      expect(savedProduct.slug).toBeDefined();
    });

    it('should generate slug from product name', async () => {
      const productData = createTestProduct({
        name: 'Test Product With Spaces & Special Characters!'
      });
      const product = new Product(productData);
      const savedProduct = await product.save();

      expect(savedProduct.slug).toBe('test-product-with-spaces-special-characters');
    });

    it('should ensure slug uniqueness', async () => {
      const productData1 = createTestProduct({ name: 'Duplicate Name' });
      const productData2 = createTestProduct({ 
        name: 'Duplicate Name',
        barcode: '9876543210987',
        sku: 'DIFF-SKU-001'
      });

      const product1 = new Product(productData1);
      const product2 = new Product(productData2);

      await product1.save();
      await product2.save();

      expect(product1.slug).toBe('duplicate-name');
      expect(product2.slug).toBe('duplicate-name-1');
    });

    it('should require mandatory fields', async () => {
      const product = new Product({});

      await expect(product.save()).rejects.toThrow();
    });

    it('should validate price range', async () => {
      const productData = createTestProduct({ price: -10 });
      const product = new Product(productData);

      await expect(product.save()).rejects.toThrow();
    });

    it('should validate stock quantity', async () => {
      const productData = createTestProduct({ stockQuantity: -5 });
      const product = new Product(productData);

      await expect(product.save()).rejects.toThrow();
    });

    it('should validate category', async () => {
      const productData = createTestProduct({ category: 'invalid-category' });
      const product = new Product(productData);

      await expect(product.save()).rejects.toThrow();
    });

    it('should validate barcode format', async () => {
      const productData = createTestProduct({ barcode: '123' }); // Too short
      const product = new Product(productData);

      await expect(product.save()).rejects.toThrow();
    });

    it('should ensure barcode uniqueness', async () => {
      const barcode = '1234567890123';
      const productData1 = createTestProduct({ barcode });
      const productData2 = createTestProduct({ 
        name: 'Different Product',
        barcode,
        sku: 'DIFF-SKU-002'
      });

      const product1 = new Product(productData1);
      const product2 = new Product(productData2);

      await product1.save();
      await expect(product2.save()).rejects.toThrow();
    });

    it('should ensure SKU uniqueness', async () => {
      const sku = 'UNIQUE-SKU-001';
      const productData1 = createTestProduct({ sku });
      const productData2 = createTestProduct({ 
        name: 'Different Product',
        barcode: '9876543210987',
        sku
      });

      const product1 = new Product(productData1);
      const product2 = new Product(productData2);

      await product1.save();
      await expect(product2.save()).rejects.toThrow();
    });
  });

  describe('Product Virtual Fields', () => {
    it('should calculate isLowStock correctly', async () => {
      const productData = createTestProduct({
        stockQuantity: 5,
        minStockLevel: 10
      });
      const product = new Product(productData);
      const savedProduct = await product.save();

      expect(savedProduct.isLowStock).toBe(true);
    });

    it('should calculate isOutOfStock correctly', async () => {
      const productData = createTestProduct({ stockQuantity: 0 });
      const product = new Product(productData);
      const savedProduct = await product.save();

      expect(savedProduct.isOutOfStock).toBe(true);
    });

    it('should calculate profit margin correctly', async () => {
      const productData = createTestProduct({
        price: 100,
        costPrice: 80
      });
      const product = new Product(productData);
      const savedProduct = await product.save();

      expect(savedProduct.profitMargin).toBe(25); // (100-80)/80 * 100 = 25%
    });

    it('should calculate stock status correctly', async () => {
      // Out of stock
      const outOfStock = new Product(createTestProduct({ stockQuantity: 0 }));
      await outOfStock.save();
      expect(outOfStock.stockStatus).toBe('out_of_stock');

      // Low stock
      const lowStock = new Product(createTestProduct({ 
        stockQuantity: 5, 
        minStockLevel: 10 
      }));
      await lowStock.save();
      expect(lowStock.stockStatus).toBe('low_stock');

      // In stock
      const inStock = new Product(createTestProduct({ 
        stockQuantity: 50, 
        minStockLevel: 10 
      }));
      await inStock.save();
      expect(inStock.stockStatus).toBe('in_stock');
    });

    it('should calculate total value correctly', async () => {
      const productData = createTestProduct({
        price: 25.50,
        stockQuantity: 10
      });
      const product = new Product(productData);
      const savedProduct = await product.save();

      expect(savedProduct.totalValue).toBe(255);
    });
  });

  describe('Product Instance Methods', () => {
    let product;

    beforeEach(async () => {
      const productData = createTestProduct({ stockQuantity: 50 });
      product = new Product(productData);
      await product.save();
    });

    it('should update stock with set operation', async () => {
      await product.updateStock(30, 'set');
      expect(product.stockQuantity).toBe(30);
    });

    it('should update stock with add operation', async () => {
      await product.updateStock(20, 'add');
      expect(product.stockQuantity).toBe(70);
    });

    it('should update stock with subtract operation', async () => {
      await product.updateStock(10, 'subtract');
      expect(product.stockQuantity).toBe(40);
    });

    it('should not allow negative stock', async () => {
      await product.updateStock(100, 'subtract');
      expect(product.stockQuantity).toBe(0);
    });

    it('should adjust price correctly', async () => {
      const oldPrice = product.price;
      await product.adjustPrice(35.00, 'Price increase');
      
      expect(product.price).toBe(35.00);
      expect(product.price).not.toBe(oldPrice);
    });

    it('should mark as discontinued', async () => {
      await product.markAsDiscontinued('Product discontinued');
      
      expect(product.isDiscontinued).toBe(true);
      expect(product.isActive).toBe(false);
      expect(product.notes).toContain('Discontinued: Product discontinued');
    });
  });

  describe('Product Static Methods', () => {
    beforeEach(async () => {
      // Create test products with different stock levels
      const products = [
        createTestProduct({ name: 'Low Stock Product', stockQuantity: 2, minStockLevel: 5 }),
        createTestProduct({ name: 'Out of Stock Product', stockQuantity: 0, minStockLevel: 5 }),
        createTestProduct({ name: 'Normal Stock Product', stockQuantity: 50, minStockLevel: 5 }),
        createTestProduct({ name: 'Inactive Product', stockQuantity: 10, isActive: false })
      ];

      for (const productData of products) {
        const product = new Product(productData);
        await product.save();
      }
    });

    it('should find low stock products', async () => {
      const lowStockProducts = await Product.findLowStock();
      expect(lowStockProducts).toHaveLength(1);
      expect(lowStockProducts[0].name).toBe('Low Stock Product');
    });

    it('should find out of stock products', async () => {
      const outOfStockProducts = await Product.findOutOfStock();
      expect(outOfStockProducts).toHaveLength(1);
      expect(outOfStockProducts[0].name).toBe('Out of Stock Product');
    });

    it('should find products by category', async () => {
      const snackProducts = await Product.findByCategory('snacks');
      expect(snackProducts.length).toBeGreaterThan(0);
      snackProducts.forEach(product => {
        expect(product.category).toBe('snacks');
        expect(product.isActive).toBe(true);
      });
    });

    it('should search products by text', async () => {
      const searchResults = await Product.searchProducts('Low Stock');
      expect(searchResults).toHaveLength(1);
      expect(searchResults[0].name).toBe('Low Stock Product');
    });

    it('should get inventory statistics', async () => {
      const stats = await Product.getInventoryStats();
      expect(stats).toHaveLength(1);
      expect(stats[0].totalProducts).toBe(3); // Only active products
      expect(stats[0].lowStockCount).toBe(1);
      expect(stats[0].outOfStockCount).toBe(1);
    });

    it('should get category statistics', async () => {
      const stats = await Product.getCategoryStats();
      expect(stats.length).toBeGreaterThan(0);
      stats.forEach(stat => {
        expect(stat._id).toBeDefined(); // Category name
        expect(stat.productCount).toBeGreaterThan(0);
        expect(stat.totalValue).toBeGreaterThanOrEqual(0);
      });
    });
  });

  describe('Product Middleware', () => {
    it('should update lastRestocked when stock increases', async () => {
      const productData = createTestProduct({ stockQuantity: 10 });
      const product = new Product(productData);
      await product.save();

      const originalDate = product.lastRestocked;

      // Increase stock
      product.stockQuantity = 20;
      await product.save();

      expect(product.lastRestocked).not.toEqual(originalDate);
    });

    it('should validate max stock level vs min stock level', async () => {
      const productData = createTestProduct({
        minStockLevel: 20,
        maxStockLevel: 10 // Invalid: max < min
      });
      const product = new Product(productData);

      await expect(product.save()).rejects.toThrow('Maximum stock level must be greater than minimum stock level');
    });
  });
});
