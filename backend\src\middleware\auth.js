const authConfig = require('../config/auth');
const User = require('../models/User');
const logger = require('../config/logger');

/**
 * Authentication middleware to verify JWT tokens
 */
const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authConfig.extractTokenFromHeader(authHeader);

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access token is required',
        code: 'TOKEN_MISSING'
      });
    }

    // Verify the token
    const decoded = authConfig.verifyAccessToken(token);
    
    // Find the user
    const user = await User.findById(decoded.id).select('-password');
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'User account is deactivated',
        code: 'ACCOUNT_DEACTIVATED'
      });
    }

    // Check if token was issued before password change
    if (user.passwordChangedAt && decoded.iat < user.passwordChangedAt.getTime() / 1000) {
      return res.status(401).json({
        success: false,
        message: 'Token is invalid due to password change',
        code: 'TOKEN_INVALID'
      });
    }

    // Attach user to request
    req.user = user;
    req.token = token;

    logger.debug('User authenticated successfully', {
      userId: user._id,
      email: user.email,
      role: user.role
    });

    next();
  } catch (error) {
    logger.warn('Authentication failed', {
      error: error.message,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    return res.status(401).json({
      success: false,
      message: error.message,
      code: 'AUTHENTICATION_FAILED'
    });
  }
};

/**
 * Authorization middleware to check user roles
 * @param {...string} roles - Allowed roles
 */
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTHENTICATION_REQUIRED'
      });
    }

    if (!roles.includes(req.user.role)) {
      logger.warn('Authorization failed', {
        userId: req.user._id,
        userRole: req.user.role,
        requiredRoles: roles,
        endpoint: req.originalUrl
      });

      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS'
      });
    }

    logger.debug('User authorized successfully', {
      userId: req.user._id,
      role: req.user.role,
      endpoint: req.originalUrl
    });

    next();
  };
};

/**
 * Optional authentication middleware
 * Attaches user if token is valid, but doesn't fail if no token
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authConfig.extractTokenFromHeader(authHeader);

    if (token) {
      const decoded = authConfig.verifyAccessToken(token);
      const user = await User.findById(decoded.id).select('-password');
      
      if (user && user.isActive) {
        req.user = user;
        req.token = token;
      }
    }

    next();
  } catch (error) {
    // Silently fail for optional auth
    logger.debug('Optional authentication failed', { error: error.message });
    next();
  }
};

/**
 * Middleware to check if user owns the resource
 * @param {string} resourceField - Field name that contains the user ID
 */
const checkOwnership = (resourceField = 'userId') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required',
          code: 'AUTHENTICATION_REQUIRED'
        });
      }

      // Admin can access any resource
      if (req.user.role === 'admin') {
        return next();
      }

      const resourceId = req.params.id;
      if (!resourceId) {
        return res.status(400).json({
          success: false,
          message: 'Resource ID is required',
          code: 'RESOURCE_ID_REQUIRED'
        });
      }

      // This would need to be implemented based on the specific model
      // For now, we'll just check if the user ID matches
      const userId = req.body[resourceField] || req.params.userId || req.query.userId;
      
      if (userId && userId !== req.user._id.toString()) {
        return res.status(403).json({
          success: false,
          message: 'Access denied to this resource',
          code: 'ACCESS_DENIED'
        });
      }

      next();
    } catch (error) {
      logger.error('Ownership check failed', { error: error.message });
      return res.status(500).json({
        success: false,
        message: 'Authorization check failed',
        code: 'AUTHORIZATION_ERROR'
      });
    }
  };
};

/**
 * Middleware to validate API key for external integrations
 */
const validateApiKey = (req, res, next) => {
  const apiKey = req.headers['x-api-key'];
  const validApiKeys = process.env.API_KEYS ? process.env.API_KEYS.split(',') : [];

  if (!apiKey || !validApiKeys.includes(apiKey)) {
    logger.warn('Invalid API key attempt', {
      providedKey: apiKey ? 'PROVIDED' : 'MISSING',
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    return res.status(401).json({
      success: false,
      message: 'Valid API key is required',
      code: 'INVALID_API_KEY'
    });
  }

  logger.debug('API key validated successfully');
  next();
};

module.exports = {
  authenticate,
  authorize,
  optionalAuth,
  checkOwnership,
  validateApiKey
};
