# Sari-Sari Store API Documentation

## Overview

The Sari-Sari Store API is a RESTful API built with Node.js, Express, and MongoDB. It provides comprehensive functionality for managing a sari-sari store including products, debts, and user management.

## Base URL

```
Development: http://localhost:5000/api
Production: https://your-domain.com/api
```

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Authentication Endpoints

#### Register User
```http
POST /api/auth/register
```

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "confirmPassword": "SecurePass123!",
  "phone": "+63-************"
}
```

#### Login
```http
POST /api/auth/login
```

**Request Body:**
```json
{
  "login": "johndoe", // username or email
  "password": "SecurePass123!"
}
```

#### Refresh Token
```http
POST /api/auth/refresh
```

**Request Body:**
```json
{
  "refreshToken": "your-refresh-token"
}
```

#### Get Current User
```http
GET /api/auth/me
```

#### Logout
```http
POST /api/auth/logout
```

## Products API

### Get All Products
```http
GET /api/products
```

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10, max: 100)
- `category` (string): Filter by category
- `search` (string): Search in name, description, tags
- `sortBy` (string): Sort field (name, price, stockQuantity, createdAt, category)
- `sortOrder` (string): Sort order (asc, desc)
- `inStock` (boolean): Filter by stock availability
- `lowStock` (boolean): Filter low stock items
- `minPrice` (number): Minimum price filter
- `maxPrice` (number): Maximum price filter

**Response:**
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 50,
    "pages": 5,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### Get Single Product
```http
GET /api/products/:id
```

### Create Product
```http
POST /api/products
```

**Request Body:**
```json
{
  "name": "Lucky Me Pancit Canton",
  "description": "Instant pancit canton noodles",
  "netWeight": "60g",
  "price": 15.00,
  "costPrice": 12.00,
  "stockQuantity": 50,
  "minStockLevel": 10,
  "maxStockLevel": 100,
  "category": "instant foods",
  "barcode": "4800016644801",
  "sku": "LM-PC-ORIG-60G",
  "tags": ["instant", "noodles", "pancit"],
  "supplier": {
    "name": "Monde Nissin Corporation",
    "contact": "+63-2-8631-8888"
  },
  "expiryDate": "2024-12-31T00:00:00.000Z"
}
```

### Update Product
```http
PUT /api/products/:id
```

### Delete Product (Soft Delete)
```http
DELETE /api/products/:id
```

### Update Product Stock
```http
PATCH /api/products/:id/stock
```

**Request Body:**
```json
{
  "quantity": 20,
  "operation": "add" // "set", "add", "subtract"
}
```

### Update Product Price
```http
PATCH /api/products/:id/price
```

**Request Body:**
```json
{
  "price": 18.00,
  "reason": "Price increase due to supplier cost"
}
```

### Get Categories
```http
GET /api/products/categories/list
```

### Get Category Statistics
```http
GET /api/products/categories/stats
```

### Get Inventory Statistics
```http
GET /api/products/inventory/stats
```

### Get Low Stock Products
```http
GET /api/products/low-stock
```

### Get Out of Stock Products
```http
GET /api/products/out-of-stock
```

### Advanced Product Search
```http
POST /api/products/search
```

**Request Body:**
```json
{
  "query": "pancit",
  "filters": {
    "category": "instant foods",
    "minPrice": 10,
    "maxPrice": 20,
    "inStock": true
  },
  "sort": {
    "price": 1
  },
  "pagination": {
    "page": 1,
    "limit": 10
  }
}
```

## Debts API

### Get All Debts
```http
GET /api/debts
```

**Query Parameters:**
- `page`, `limit`: Pagination
- `customerName`: Filter by customer
- `isPaid`: Filter by payment status
- `status`: Filter by debt status
- `sortBy`: Sort field
- `sortOrder`: Sort order

### Get Single Debt
```http
GET /api/debts/:id
```

### Create Debt
```http
POST /api/debts
```

**Request Body:**
```json
{
  "customerName": "Maria Santos",
  "customerPhone": "+63-************",
  "customerEmail": "<EMAIL>",
  "productId": "product-object-id",
  "quantity": 2,
  "dateOfDebt": "2024-01-15T00:00:00.000Z",
  "dueDate": "2024-02-15T00:00:00.000Z",
  "notes": "Regular customer",
  "priority": "medium",
  "tags": ["regular-customer"]
}
```

### Update Debt
```http
PUT /api/debts/:id
```

### Delete Debt
```http
DELETE /api/debts/:id
```

### Mark Debt as Paid
```http
PATCH /api/debts/:id/pay
```

**Request Body:**
```json
{
  "amountPaid": 45.00,
  "paymentMethod": "cash",
  "notes": "Paid in full"
}
```

## Error Responses

All error responses follow this format:

```json
{
  "success": false,
  "error": {
    "name": "ValidationError",
    "message": "Validation failed",
    "code": "VALIDATION_FAILED",
    "errors": [
      {
        "field": "price",
        "message": "Price must be a positive number",
        "value": -10
      }
    ],
    "timestamp": "2024-01-15T10:30:00.000Z",
    "path": "/api/products",
    "method": "POST"
  }
}
```

## Status Codes

- `200` - OK
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Unprocessable Entity
- `423` - Locked
- `429` - Too Many Requests
- `500` - Internal Server Error
- `503` - Service Unavailable

## Rate Limiting

- Authentication endpoints: 5 requests per 15 minutes
- General API endpoints: 100 requests per 15 minutes
- File upload endpoints: 10 requests per 15 minutes

## File Uploads

Product images can be uploaded using multipart/form-data:

```http
POST /api/products
Content-Type: multipart/form-data

{
  "image": <file>,
  "name": "Product Name",
  ...other fields
}
```

**Supported formats:** JPEG, JPG, PNG, GIF, WebP
**Maximum file size:** 5MB
**Image processing:** Automatically resized to 800x600 and converted to WebP

## Pagination

All list endpoints support pagination:

```json
{
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 50,
    "pages": 5,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## Filtering and Sorting

Most list endpoints support filtering and sorting through query parameters:

```http
GET /api/products?category=snacks&sortBy=price&sortOrder=asc&page=1&limit=20
```

## Health Check

```http
GET /api/health
```

Returns server status, database connection, and system information.

## API Versioning

The current API version is v1. Future versions will be available at:
```
/api/v2/...
```

## SDKs and Libraries

- JavaScript/Node.js: Coming soon
- PHP: Coming soon
- Python: Coming soon

## Support

For API support, please contact: <EMAIL>
