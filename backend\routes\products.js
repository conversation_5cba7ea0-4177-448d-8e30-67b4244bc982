const express = require('express');
const { body, validationResult, param, query } = require('express-validator');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const sharp = require('sharp');
const Product = require('../models/Product');
const { catchAsync, handleValidationError } = require('../src/middleware/errorHandler');
const { uploadLimiter } = require('../src/middleware/security');
const { cache, CacheKeys } = require('../src/utils/cache');
const queryOptimizer = require('../src/utils/queryOptimizer');
const logger = require('../src/config/logger');

const router = express.Router();

// Configure multer for image uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads/products');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
      logger.info(`Created upload directory: ${uploadDir}`);
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const sanitizedName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '');
    cb(null, 'product-' + uniqueSuffix + path.extname(sanitizedName));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024, // 5MB default
    files: 1
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = process.env.ALLOWED_FILE_TYPES
      ? new RegExp(process.env.ALLOWED_FILE_TYPES.replace(/,/g, '|'))
      : /jpeg|jpg|png|gif|webp/;

    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype.split('/')[1]);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      const error = new Error('Only image files are allowed (jpeg, jpg, png, gif, webp)');
      error.code = 'INVALID_FILE_TYPE';
      cb(error);
    }
  }
});

// Image processing middleware
const processImage = catchAsync(async (req, res, next) => {
  if (!req.file) return next();

  try {
    const filename = `product-${Date.now()}-${Math.round(Math.random() * 1E9)}.webp`;
    const outputPath = path.join(__dirname, '../uploads/products', filename);

    // Process image with sharp
    await sharp(req.file.path)
      .resize(800, 600, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .webp({ quality: 80 })
      .toFile(outputPath);

    // Remove original file
    fs.unlinkSync(req.file.path);

    // Update req.file with processed image info
    req.file.filename = filename;
    req.file.path = outputPath;

    logger.info('Image processed successfully', {
      originalName: req.file.originalname,
      processedName: filename,
      size: req.file.size
    });

    next();
  } catch (error) {
    logger.error('Image processing failed', error);
    // Remove uploaded file if processing fails
    if (fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    next(error);
  }
});

// Validation rules
const productValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Product name must be between 2 and 100 characters')
    .matches(/^[a-zA-Z0-9\s\-&.()]+$/)
    .withMessage('Product name contains invalid characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
  body('netWeight')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Net weight must be between 1 and 50 characters')
    .matches(/^[\d.]+\s*(g|kg|ml|l|oz|lb|pcs?|pieces?)$/i)
    .withMessage('Net weight must include a valid unit (g, kg, ml, l, oz, lb, pcs)'),
  body('price')
    .isFloat({ min: 0.01, max: 999999.99 })
    .withMessage('Price must be between 0.01 and 999,999.99'),
  body('costPrice')
    .optional()
    .isFloat({ min: 0, max: 999999.99 })
    .withMessage('Cost price must be between 0 and 999,999.99'),
  body('stockQuantity')
    .isInt({ min: 0, max: 999999 })
    .withMessage('Stock quantity must be between 0 and 999,999'),
  body('minStockLevel')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Minimum stock level must be a non-negative integer'),
  body('maxStockLevel')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Maximum stock level must be a non-negative integer'),
  body('category')
    .isIn(['snacks', 'canned goods', 'beverages', 'personal care', 'household items', 'condiments', 'dairy', 'frozen goods', 'bread & pastries', 'instant foods', 'others'])
    .withMessage('Please select a valid category'),
  body('barcode')
    .optional()
    .matches(/^[0-9]{8,13}$/)
    .withMessage('Barcode must be 8-13 digits'),
  body('sku')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('SKU cannot exceed 50 characters'),
  body('supplier.name')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Supplier name cannot exceed 100 characters'),
  body('supplier.contact')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Supplier contact cannot exceed 100 characters'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('tags.*')
    .optional()
    .trim()
    .isLength({ max: 30 })
    .withMessage('Each tag cannot exceed 30 characters'),
  body('expiryDate')
    .optional()
    .isISO8601()
    .withMessage('Expiry date must be a valid date')
    .custom((value) => {
      if (new Date(value) <= new Date()) {
        throw new Error('Expiry date must be in the future');
      }
      return true;
    })
];

// Validation middleware
const validateProduct = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation errors',
      errors: errors.array().map(error => ({
        field: error.path || error.param,
        message: error.msg,
        value: error.value
      }))
    });
  }
  next();
};

// GET /api/products - Get all products with optional filtering
router.get('/', [
  query('category').optional().isString().trim(),
  query('search').optional().isString().trim().isLength({ max: 100 }),
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('sortBy').optional().isIn(['name', 'price', 'stockQuantity', 'createdAt', 'category']),
  query('sortOrder').optional().isIn(['asc', 'desc']),
  query('inStock').optional().isBoolean().toBoolean(),
  query('lowStock').optional().isBoolean().toBoolean(),
  query('minPrice').optional().isFloat({ min: 0 }).toFloat(),
  query('maxPrice').optional().isFloat({ min: 0 }).toFloat()
], validateProduct, catchAsync(async (req, res) => {
  const filters = req.query;
  const pagination = { page: filters.page || 1, limit: filters.limit || 10 };
  const sort = { sortBy: filters.sortBy || 'createdAt', sortOrder: filters.sortOrder || 'desc' };

  // Generate cache key
  const cacheKey = CacheKeys.products({ filters, pagination, sort });

  // Try to get from cache first
  const cachedResult = await cache.get(cacheKey);
  if (cachedResult) {
    logger.debug('Serving products from cache', { cacheKey });
    return res.json(cachedResult);
  }

  // Optimize query
  const queryOptions = queryOptimizer.optimizeProductQuery(filters, pagination, sort);

  // Execute optimized queries
  const [products, total] = await Promise.all([
    queryOptimizer.executeQuery(Product, queryOptions.query, {
      sort: queryOptions.sort,
      skip: queryOptions.skip,
      limit: queryOptions.limit,
      select: queryOptions.select
    }),
    Product.countDocuments(queryOptions.query)
  ]);

  // Build response
  const response = {
    success: true,
    data: products,
    pagination: {
      page: queryOptions.page,
      limit: queryOptions.limit,
      total,
      pages: Math.ceil(total / queryOptions.limit),
      hasNext: queryOptions.page < Math.ceil(total / queryOptions.limit),
      hasPrev: queryOptions.page > 1
    },
    filters: {
      category: filters.category,
      search: filters.search,
      inStock: filters.inStock,
      lowStock: filters.lowStock,
      minPrice: filters.minPrice,
      maxPrice: filters.maxPrice
    },
    sort: {
      sortBy: sort.sortBy,
      sortOrder: sort.sortOrder
    }
  };

  // Add pagination headers
  res.set('X-Total-Count', total.toString());
  res.set('X-Page-Count', Math.ceil(total / queryOptions.limit).toString());

  // Cache the result (5 minutes for product lists)
  await cache.set(cacheKey, response, 300);

  res.json(response);
}));

// GET /api/products/:id - Get single product
router.get('/:id', [
  param('id').isMongoId().withMessage('Invalid product ID')
], validateProduct, catchAsync(async (req, res) => {
  const productId = req.params.id;
  const cacheKey = CacheKeys.product(productId);

  // Try to get from cache first
  const cachedProduct = await cache.get(cacheKey);
  if (cachedProduct) {
    logger.debug('Serving product from cache', { productId, cacheKey });
    return res.json(cachedProduct);
  }

  const product = await Product.findById(productId)
    .select('-__v');

  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found',
      code: 'PRODUCT_NOT_FOUND'
    });
  }

  // Check if product is active
  if (!product.isActive) {
    return res.status(404).json({
      success: false,
      message: 'Product is no longer available',
      code: 'PRODUCT_INACTIVE'
    });
  }

  const response = {
    success: true,
    data: product
  };

  // Cache the result (10 minutes for individual products)
  await cache.set(cacheKey, response, 600);

  res.json(response);
}));

// POST /api/products - Create new product
router.post('/',
  uploadLimiter,
  upload.single('image'),
  processImage,
  productValidation,
  validateProduct,
  catchAsync(async (req, res) => {
    // Check for duplicate product name
    const existingProduct = await Product.findOne({
      name: { $regex: new RegExp(`^${req.body.name}$`, 'i') },
      isActive: true
    });

    if (existingProduct) {
      return res.status(400).json({
        success: false,
        message: 'A product with this name already exists',
        code: 'DUPLICATE_PRODUCT_NAME'
      });
    }

    // Check for duplicate barcode if provided
    if (req.body.barcode) {
      const existingBarcode = await Product.findOne({
        barcode: req.body.barcode,
        isActive: true
      });

      if (existingBarcode) {
        return res.status(400).json({
          success: false,
          message: 'A product with this barcode already exists',
          code: 'DUPLICATE_BARCODE'
        });
      }
    }

    // Check for duplicate SKU if provided
    if (req.body.sku) {
      const existingSKU = await Product.findOne({
        sku: req.body.sku.toUpperCase(),
        isActive: true
      });

      if (existingSKU) {
        return res.status(400).json({
          success: false,
          message: 'A product with this SKU already exists',
          code: 'DUPLICATE_SKU'
        });
      }
    }

    // Build product data
    const productData = {
      name: req.body.name.trim(),
      description: req.body.description?.trim() || '',
      netWeight: req.body.netWeight.trim(),
      price: parseFloat(req.body.price),
      costPrice: req.body.costPrice ? parseFloat(req.body.costPrice) : 0,
      stockQuantity: parseInt(req.body.stockQuantity) || 0,
      minStockLevel: req.body.minStockLevel ? parseInt(req.body.minStockLevel) : 5,
      maxStockLevel: req.body.maxStockLevel ? parseInt(req.body.maxStockLevel) : null,
      category: req.body.category.toLowerCase(),
      barcode: req.body.barcode || null,
      sku: req.body.sku ? req.body.sku.toUpperCase() : null,
      tags: req.body.tags ? (Array.isArray(req.body.tags) ? req.body.tags : req.body.tags.split(',').map(tag => tag.trim().toLowerCase())) : [],
      expiryDate: req.body.expiryDate ? new Date(req.body.expiryDate) : null
    };

    // Handle supplier data
    if (req.body['supplier.name'] || req.body['supplier.contact']) {
      productData.supplier = {
        name: req.body['supplier.name']?.trim() || '',
        contact: req.body['supplier.contact']?.trim() || ''
      };
    }

    // Handle image
    if (req.file) {
      productData.image = `/uploads/products/${req.file.filename}`;
    } else if (req.body.imageUrl) {
      productData.image = req.body.imageUrl;
    }

    // Create product
    const product = new Product(productData);
    await product.save();

    // Invalidate related caches
    await Promise.all([
      cache.invalidatePattern('products:*'),
      cache.invalidatePattern('product:stats*'),
      cache.invalidatePattern('product:categories*')
    ]);

    logger.info('Product created successfully', {
      productId: product._id,
      productName: product.name,
      category: product.category,
      price: product.price,
      stockQuantity: product.stockQuantity
    });

    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      data: product
    });
  })
);

// PUT /api/products/:id - Update product
router.put('/:id', [
  param('id').isMongoId().withMessage('Invalid product ID'),
  uploadLimiter,
  upload.single('image'),
  processImage,
  ...productValidation
], validateProduct, catchAsync(async (req, res) => {
  const product = await Product.findById(req.params.id);

  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found',
      code: 'PRODUCT_NOT_FOUND'
    });
  }

  // Check for duplicate product name (excluding current product)
  if (req.body.name && req.body.name !== product.name) {
    const existingProduct = await Product.findOne({
      name: { $regex: new RegExp(`^${req.body.name}$`, 'i') },
      _id: { $ne: req.params.id },
      isActive: true
    });

    if (existingProduct) {
      return res.status(400).json({
        success: false,
        message: 'Another product with this name already exists',
        code: 'DUPLICATE_PRODUCT_NAME'
      });
    }
  }

  // Check for duplicate barcode (excluding current product)
  if (req.body.barcode && req.body.barcode !== product.barcode) {
    const existingBarcode = await Product.findOne({
      barcode: req.body.barcode,
      _id: { $ne: req.params.id },
      isActive: true
    });

    if (existingBarcode) {
      return res.status(400).json({
        success: false,
        message: 'Another product with this barcode already exists',
        code: 'DUPLICATE_BARCODE'
      });
    }
  }

  // Check for duplicate SKU (excluding current product)
  if (req.body.sku && req.body.sku.toUpperCase() !== product.sku) {
    const existingSKU = await Product.findOne({
      sku: req.body.sku.toUpperCase(),
      _id: { $ne: req.params.id },
      isActive: true
    });

    if (existingSKU) {
      return res.status(400).json({
        success: false,
        message: 'Another product with this SKU already exists',
        code: 'DUPLICATE_SKU'
      });
    }
  }

  // Build update data
  const updateData = {
    name: req.body.name?.trim() || product.name,
    description: req.body.description?.trim() || product.description,
    netWeight: req.body.netWeight?.trim() || product.netWeight,
    price: req.body.price ? parseFloat(req.body.price) : product.price,
    costPrice: req.body.costPrice !== undefined ? parseFloat(req.body.costPrice) : product.costPrice,
    stockQuantity: req.body.stockQuantity !== undefined ? parseInt(req.body.stockQuantity) : product.stockQuantity,
    minStockLevel: req.body.minStockLevel !== undefined ? parseInt(req.body.minStockLevel) : product.minStockLevel,
    maxStockLevel: req.body.maxStockLevel !== undefined ? parseInt(req.body.maxStockLevel) : product.maxStockLevel,
    category: req.body.category?.toLowerCase() || product.category,
    barcode: req.body.barcode || product.barcode,
    sku: req.body.sku ? req.body.sku.toUpperCase() : product.sku,
    tags: req.body.tags ? (Array.isArray(req.body.tags) ? req.body.tags : req.body.tags.split(',').map(tag => tag.trim().toLowerCase())) : product.tags,
    expiryDate: req.body.expiryDate ? new Date(req.body.expiryDate) : product.expiryDate,
    updatedBy: req.user?.id || null // Will be set when auth is implemented
  };

  // Handle supplier data
  if (req.body['supplier.name'] !== undefined || req.body['supplier.contact'] !== undefined) {
    updateData.supplier = {
      name: req.body['supplier.name']?.trim() || product.supplier?.name || '',
      contact: req.body['supplier.contact']?.trim() || product.supplier?.contact || ''
    };
  }

  // Handle image update
  if (req.file) {
    // Remove old image if it exists
    if (product.image && product.image.startsWith('/uploads/')) {
      const oldImagePath = path.join(__dirname, '..', product.image);
      if (fs.existsSync(oldImagePath)) {
        fs.unlinkSync(oldImagePath);
      }
    }
    updateData.image = `/uploads/products/${req.file.filename}`;
  } else if (req.body.imageUrl) {
    updateData.image = req.body.imageUrl;
  } else if (req.body.removeImage === 'true') {
    // Remove existing image
    if (product.image && product.image.startsWith('/uploads/')) {
      const oldImagePath = path.join(__dirname, '..', product.image);
      if (fs.existsSync(oldImagePath)) {
        fs.unlinkSync(oldImagePath);
      }
    }
    updateData.image = null;
  }

  const updatedProduct = await Product.findByIdAndUpdate(
    req.params.id,
    updateData,
    { new: true, runValidators: true }
  ).select('-__v');

  // Invalidate related caches
  await Promise.all([
    cache.del(CacheKeys.product(req.params.id)),
    cache.invalidatePattern('products:*'),
    cache.invalidatePattern('product:stats*'),
    cache.invalidatePattern('product:categories*')
  ]);

  logger.info('Product updated successfully', {
    productId: updatedProduct._id,
    productName: updatedProduct.name,
    changes: Object.keys(updateData)
  });

  res.json({
    success: true,
    message: 'Product updated successfully',
    data: updatedProduct
  });
}));

// DELETE /api/products/:id - Delete product (soft delete)
router.delete('/:id', [
  param('id').isMongoId().withMessage('Invalid product ID')
], validateProduct, catchAsync(async (req, res) => {
  const product = await Product.findById(req.params.id);

  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found',
      code: 'PRODUCT_NOT_FOUND'
    });
  }

  if (!product.isActive) {
    return res.status(400).json({
      success: false,
      message: 'Product is already deleted',
      code: 'PRODUCT_ALREADY_DELETED'
    });
  }

  // Soft delete - mark as inactive
  product.isActive = false;
  product.updatedBy = req.user?.id || null; // Will be set when auth is implemented
  await product.save();

  // Invalidate related caches
  await Promise.all([
    cache.del(CacheKeys.product(product._id)),
    cache.invalidatePattern('products:*'),
    cache.invalidatePattern('product:stats*'),
    cache.invalidatePattern('product:categories*')
  ]);

  logger.info('Product deleted successfully', {
    productId: product._id,
    productName: product.name
  });

  res.json({
    success: true,
    message: 'Product deleted successfully',
    data: {
      id: product._id,
      name: product.name,
      deletedAt: new Date()
    }
  });
}));

// GET /api/products/categories/list - Get all categories
router.get('/categories/list', catchAsync(async (req, res) => {
  const categories = [
    'snacks',
    'canned goods',
    'beverages',
    'personal care',
    'household items',
    'condiments',
    'dairy',
    'frozen goods',
    'bread & pastries',
    'instant foods',
    'others'
  ];

  res.json({
    success: true,
    data: categories
  });
}));

// GET /api/products/categories/stats - Get category statistics
router.get('/categories/stats', catchAsync(async (req, res) => {
  const stats = await Product.getCategoryStats();

  res.json({
    success: true,
    data: stats
  });
}));

// GET /api/products/inventory/stats - Get inventory statistics
router.get('/inventory/stats', catchAsync(async (req, res) => {
  const cacheKey = CacheKeys.productStats();

  // Try to get from cache first
  const cachedStats = await cache.get(cacheKey);
  if (cachedStats) {
    logger.debug('Serving inventory stats from cache', { cacheKey });
    return res.json(cachedStats);
  }

  const stats = await Product.getInventoryStats();

  const response = {
    success: true,
    data: stats[0] || {
      totalProducts: 0,
      totalValue: 0,
      totalStock: 0,
      lowStockCount: 0,
      outOfStockCount: 0,
      avgPrice: 0,
      categoryCount: 0
    }
  };

  // Cache for 5 minutes
  await cache.set(cacheKey, response, 300);

  res.json(response);
}));

// GET /api/products/low-stock - Get low stock products
router.get('/low-stock', catchAsync(async (req, res) => {
  const products = await Product.findLowStock();

  res.json({
    success: true,
    data: products,
    count: products.length
  });
}));

// GET /api/products/out-of-stock - Get out of stock products
router.get('/out-of-stock', catchAsync(async (req, res) => {
  const products = await Product.findOutOfStock();

  res.json({
    success: true,
    data: products,
    count: products.length
  });
}));

// PATCH /api/products/:id/stock - Update product stock
router.patch('/:id/stock', [
  param('id').isMongoId().withMessage('Invalid product ID'),
  body('quantity').isInt({ min: 0 }).withMessage('Quantity must be a non-negative integer'),
  body('operation').optional().isIn(['set', 'add', 'subtract']).withMessage('Operation must be set, add, or subtract')
], validateProduct, catchAsync(async (req, res) => {
  const { quantity, operation = 'set' } = req.body;

  const product = await Product.findById(req.params.id);

  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found',
      code: 'PRODUCT_NOT_FOUND'
    });
  }

  if (!product.isActive) {
    return res.status(400).json({
      success: false,
      message: 'Cannot update stock for inactive product',
      code: 'PRODUCT_INACTIVE'
    });
  }

  const oldStock = product.stockQuantity;
  await product.updateStock(quantity, operation);

  logger.info('Product stock updated', {
    productId: product._id,
    productName: product.name,
    oldStock,
    newStock: product.stockQuantity,
    operation,
    quantity
  });

  res.json({
    success: true,
    message: 'Stock updated successfully',
    data: {
      id: product._id,
      name: product.name,
      oldStock,
      newStock: product.stockQuantity,
      operation,
      quantity
    }
  });
}));

// PATCH /api/products/:id/price - Update product price
router.patch('/:id/price', [
  param('id').isMongoId().withMessage('Invalid product ID'),
  body('price').isFloat({ min: 0.01, max: 999999.99 }).withMessage('Price must be between 0.01 and 999,999.99'),
  body('reason').optional().isString().trim().isLength({ max: 200 }).withMessage('Reason cannot exceed 200 characters')
], validateProduct, catchAsync(async (req, res) => {
  const { price, reason = 'Price update' } = req.body;

  const product = await Product.findById(req.params.id);

  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Product not found',
      code: 'PRODUCT_NOT_FOUND'
    });
  }

  if (!product.isActive) {
    return res.status(400).json({
      success: false,
      message: 'Cannot update price for inactive product',
      code: 'PRODUCT_INACTIVE'
    });
  }

  const oldPrice = product.price;
  await product.adjustPrice(price, reason);

  res.json({
    success: true,
    message: 'Price updated successfully',
    data: {
      id: product._id,
      name: product.name,
      oldPrice,
      newPrice: product.price,
      reason
    }
  });
}));

// POST /api/products/search - Advanced product search
router.post('/search', [
  body('query').optional().isString().trim().isLength({ max: 100 }),
  body('filters').optional().isObject(),
  body('sort').optional().isObject(),
  body('pagination').optional().isObject()
], validateProduct, catchAsync(async (req, res) => {
  const { query, filters = {}, sort = { createdAt: -1 }, pagination = { page: 1, limit: 10 } } = req.body;

  let searchFilter = { isActive: true, isDiscontinued: false };

  // Apply text search
  if (query) {
    searchFilter.$or = [
      { name: { $regex: query, $options: 'i' } },
      { description: { $regex: query, $options: 'i' } },
      { category: { $regex: query, $options: 'i' } },
      { tags: { $in: [new RegExp(query, 'i')] } }
    ];
  }

  // Apply filters
  if (filters.category) searchFilter.category = filters.category.toLowerCase();
  if (filters.minPrice !== undefined) searchFilter.price = { ...searchFilter.price, $gte: filters.minPrice };
  if (filters.maxPrice !== undefined) searchFilter.price = { ...searchFilter.price, $lte: filters.maxPrice };
  if (filters.inStock === true) searchFilter.stockQuantity = { $gt: 0 };
  if (filters.lowStock === true) searchFilter.$expr = { $lte: ['$stockQuantity', '$minStockLevel'] };

  const skip = (pagination.page - 1) * pagination.limit;

  const [products, total] = await Promise.all([
    Product.find(searchFilter)
      .sort(sort)
      .skip(skip)
      .limit(pagination.limit)
      .select('-__v'),
    Product.countDocuments(searchFilter)
  ]);

  res.json({
    success: true,
    data: products,
    pagination: {
      page: pagination.page,
      limit: pagination.limit,
      total,
      pages: Math.ceil(total / pagination.limit)
    },
    query,
    filters,
    sort
  });
}));

module.exports = router;
