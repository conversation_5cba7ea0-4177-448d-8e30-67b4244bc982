const mongoose = require('mongoose');
const Product = require('../models/Product');
const Debt = require('../models/Debt');
const logger = require('../src/config/logger');
require('dotenv').config();

/**
 * Database Migration Script
 * Adds indexes, optimizes schemas, and performs data migrations
 */

class DatabaseMigration {
  constructor() {
    this.migrations = [
      { name: 'addProductIndexes', version: '1.0.0', fn: this.addProductIndexes },
      { name: 'addDebtIndexes', version: '1.0.0', fn: this.addDebtIndexes },
      { name: 'optimizeTextSearch', version: '1.1.0', fn: this.optimizeTextSearch },
      { name: 'addCompoundIndexes', version: '1.2.0', fn: this.addCompoundIndexes },
      { name: 'cleanupData', version: '1.3.0', fn: this.cleanupData }
    ];
  }

  async connect() {
    try {
      await mongoose.connect(process.env.MONGODB_URI);
      logger.info('Connected to MongoDB for migration');
    } catch (error) {
      logger.error('Failed to connect to MongoDB:', error);
      throw error;
    }
  }

  async disconnect() {
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  }

  async addProductIndexes() {
    logger.info('Adding Product indexes...');
    
    const collection = mongoose.connection.db.collection('products');
    
    // Single field indexes
    await collection.createIndex({ name: 1 });
    await collection.createIndex({ category: 1 });
    await collection.createIndex({ price: 1 });
    await collection.createIndex({ stockQuantity: 1 });
    await collection.createIndex({ isActive: 1 });
    await collection.createIndex({ isDiscontinued: 1 });
    await collection.createIndex({ barcode: 1 }, { unique: true, sparse: true });
    await collection.createIndex({ sku: 1 }, { unique: true, sparse: true });
    await collection.createIndex({ createdAt: -1 });
    await collection.createIndex({ updatedAt: -1 });
    
    logger.info('Product indexes added successfully');
  }

  async addDebtIndexes() {
    logger.info('Adding Debt indexes...');
    
    const collection = mongoose.connection.db.collection('debts');
    
    // Single field indexes
    await collection.createIndex({ customerName: 1 });
    await collection.createIndex({ productId: 1 });
    await collection.createIndex({ isPaid: 1 });
    await collection.createIndex({ status: 1 });
    await collection.createIndex({ dateOfDebt: -1 });
    await collection.createIndex({ dueDate: 1 });
    await collection.createIndex({ createdAt: -1 });
    await collection.createIndex({ priority: 1 });
    
    logger.info('Debt indexes added successfully');
  }

  async optimizeTextSearch() {
    logger.info('Optimizing text search...');
    
    const productCollection = mongoose.connection.db.collection('products');
    
    // Text search index for products
    await productCollection.createIndex(
      { 
        name: 'text', 
        description: 'text',
        tags: 'text'
      },
      {
        weights: {
          name: 10,
          description: 5,
          tags: 1
        },
        name: 'product_text_search'
      }
    );
    
    logger.info('Text search optimization completed');
  }

  async addCompoundIndexes() {
    logger.info('Adding compound indexes...');
    
    const productCollection = mongoose.connection.db.collection('products');
    const debtCollection = mongoose.connection.db.collection('debts');
    
    // Product compound indexes
    await productCollection.createIndex({ category: 1, isActive: 1 });
    await productCollection.createIndex({ stockQuantity: 1, minStockLevel: 1 });
    await productCollection.createIndex({ isActive: 1, createdAt: -1 });
    await productCollection.createIndex({ category: 1, price: 1 });
    await productCollection.createIndex({ isActive: 1, isDiscontinued: 1, stockQuantity: 1 });
    
    // Debt compound indexes
    await debtCollection.createIndex({ customerName: 1, isPaid: 1 });
    await debtCollection.createIndex({ customerName: 1, status: 1 });
    await debtCollection.createIndex({ status: 1, dueDate: 1 });
    await debtCollection.createIndex({ isPaid: 1, dateOfDebt: -1 });
    await debtCollection.createIndex({ productId: 1, dateOfDebt: -1 });
    await debtCollection.createIndex({ customerName: 1, dateOfDebt: -1 });
    
    logger.info('Compound indexes added successfully');
  }

  async cleanupData() {
    logger.info('Cleaning up data...');
    
    // Remove products with invalid data
    const invalidProducts = await Product.find({
      $or: [
        { name: { $exists: false } },
        { name: '' },
        { price: { $lte: 0 } },
        { stockQuantity: { $lt: 0 } }
      ]
    });
    
    if (invalidProducts.length > 0) {
      logger.warn(`Found ${invalidProducts.length} invalid products, marking as inactive`);
      await Product.updateMany(
        { _id: { $in: invalidProducts.map(p => p._id) } },
        { isActive: false }
      );
    }
    
    // Remove debts with invalid data
    const invalidDebts = await Debt.find({
      $or: [
        { customerName: { $exists: false } },
        { customerName: '' },
        { productId: { $exists: false } },
        { quantity: { $lte: 0 } },
        { priceAtTimeOfDebt: { $lte: 0 } }
      ]
    });
    
    if (invalidDebts.length > 0) {
      logger.warn(`Found ${invalidDebts.length} invalid debts, removing them`);
      await Debt.deleteMany({ _id: { $in: invalidDebts.map(d => d._id) } });
    }
    
    // Update debt status based on payment
    await Debt.updateMany(
      { amountPaid: { $gte: '$totalAmount' }, isPaid: false },
      { isPaid: true, status: 'paid' }
    );
    
    logger.info('Data cleanup completed');
  }

  async getIndexInfo() {
    const productIndexes = await mongoose.connection.db.collection('products').indexes();
    const debtIndexes = await mongoose.connection.db.collection('debts').indexes();
    
    return {
      products: productIndexes,
      debts: debtIndexes
    };
  }

  async getCollectionStats() {
    const productStats = await mongoose.connection.db.collection('products').stats();
    const debtStats = await mongoose.connection.db.collection('debts').stats();
    
    return {
      products: {
        count: productStats.count,
        size: productStats.size,
        avgObjSize: productStats.avgObjSize,
        indexSizes: productStats.indexSizes
      },
      debts: {
        count: debtStats.count,
        size: debtStats.size,
        avgObjSize: debtStats.avgObjSize,
        indexSizes: debtStats.indexSizes
      }
    };
  }

  async runMigrations() {
    logger.info('Starting database migrations...');
    
    for (const migration of this.migrations) {
      try {
        logger.info(`Running migration: ${migration.name} (v${migration.version})`);
        await migration.fn.call(this);
        logger.info(`✅ Migration ${migration.name} completed successfully`);
      } catch (error) {
        logger.error(`❌ Migration ${migration.name} failed:`, error);
        throw error;
      }
    }
    
    logger.info('All migrations completed successfully');
  }

  async analyze() {
    logger.info('Analyzing database performance...');
    
    const stats = await this.getCollectionStats();
    const indexes = await this.getIndexInfo();
    
    logger.info('Database Analysis Results:', {
      collections: stats,
      indexCounts: {
        products: indexes.products.length,
        debts: indexes.debts.length
      }
    });
    
    return { stats, indexes };
  }
}

async function main() {
  const migration = new DatabaseMigration();
  
  try {
    await migration.connect();
    
    const command = process.argv[2];
    
    switch (command) {
      case 'migrate':
        await migration.runMigrations();
        break;
      case 'analyze':
        await migration.analyze();
        break;
      case 'indexes':
        const indexes = await migration.getIndexInfo();
        console.log('Current Indexes:', JSON.stringify(indexes, null, 2));
        break;
      case 'stats':
        const stats = await migration.getCollectionStats();
        console.log('Collection Statistics:', JSON.stringify(stats, null, 2));
        break;
      default:
        console.log('Usage: node migrate-database.js [migrate|analyze|indexes|stats]');
        console.log('  migrate  - Run all database migrations');
        console.log('  analyze  - Analyze database performance');
        console.log('  indexes  - Show current indexes');
        console.log('  stats    - Show collection statistics');
    }
    
  } catch (error) {
    logger.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await migration.disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = DatabaseMigration;
