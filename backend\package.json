{"name": "sari-sari-store-backend", "version": "2.0.0", "description": "Professional Backend API for Sari-Sari Store Admin Dashboard", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node seed-data.js", "test-connection": "node test-connection.js", "test": "jest --coverage", "test:watch": "jest --watch", "test:unit": "jest --testPathPattern=tests/unit", "test:integration": "jest --testPathPattern=tests/integration", "test:e2e": "jest --testPathPattern=tests/e2e", "test:ci": "jest --coverage --watchAll=false --ci", "migrate": "node scripts/migrate-database.js migrate", "db:analyze": "node scripts/migrate-database.js analyze", "db:indexes": "node scripts/migrate-database.js indexes", "db:stats": "node scripts/migrate-database.js stats", "lint": "eslint . --ext .js", "lint:fix": "eslint . --ext .js --fix", "format": "prettier --write .", "format:check": "prettier --check .", "validate": "npm run lint && npm run test:ci", "docker:build": "docker build -t sari-sari-backend .", "docker:run": "docker run -p 5000:5000 sari-sari-backend"}, "keywords": ["sari-sari", "store", "inventory", "debt", "management", "api", "mongodb", "express"], "author": "Professional Developer", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "express-validator": "^7.0.1", "helmet": "^7.1.0", "morgan": "^1.10.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "joi": "^17.11.0", "express-async-errors": "^3.1.1", "sharp": "^0.32.6", "redis": "^4.6.10", "nodemailer": "^6.9.7", "uuid": "^9.0.1", "moment": "^2.29.4", "mime-types": "^2.1.35"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-node": "^4.1.0", "eslint-plugin-node": "^11.1.0", "prettier": "^3.1.0", "mongodb-memory-server": "^9.1.1", "@types/jest": "^29.5.8", "jest-html-reporters": "^3.1.5", "jest-watch-typeahead": "^2.2.2", "eslint-plugin-security": "^1.7.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}