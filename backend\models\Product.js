const mongoose = require('mongoose');
const validator = require('validator');

// Product categories enum
const PRODUCT_CATEGORIES = [
  'snacks',
  'canned goods',
  'beverages',
  'personal care',
  'household items',
  'condiments',
  'dairy',
  'frozen goods',
  'bread & pastries',
  'instant foods',
  'others'
];

// Low stock threshold
const LOW_STOCK_THRESHOLD = 5;

const productSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    minlength: [2, 'Product name must be at least 2 characters'],
    maxlength: [100, 'Product name cannot exceed 100 characters'],
    validate: {
      validator: function(value) {
        return /^[a-zA-Z0-9\s\-&.()]+$/.test(value);
      },
      message: 'Product name contains invalid characters'
    }
  },
  slug: {
    type: String,
    unique: true,
    lowercase: true,
    trim: true
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters'],
    default: ''
  },
  image: {
    type: String,
    default: null,
    validate: {
      validator: function(value) {
        if (!value) return true;
        return validator.isURL(value) || /^\/uploads\//.test(value);
      },
      message: 'Image must be a valid URL or file path'
    }
  },
  images: [{
    type: String,
    validate: {
      validator: function(value) {
        return validator.isURL(value) || /^\/uploads\//.test(value);
      },
      message: 'Image must be a valid URL or file path'
    }
  }],
  netWeight: {
    type: String,
    required: [true, 'Net weight is required'],
    trim: true,
    maxlength: [50, 'Net weight cannot exceed 50 characters'],
    validate: {
      validator: function(value) {
        return /^[\d.]+\s*(g|kg|ml|l|oz|lb|pcs?|pieces?)$/i.test(value);
      },
      message: 'Net weight must include a valid unit (g, kg, ml, l, oz, lb, pcs)'
    }
  },
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: [0.01, 'Price must be at least 0.01'],
    max: [999999.99, 'Price cannot exceed 999,999.99'],
    validate: {
      validator: function(value) {
        return Number.isFinite(value) && value > 0;
      },
      message: 'Price must be a valid positive number'
    }
  },
  costPrice: {
    type: Number,
    min: [0, 'Cost price cannot be negative'],
    max: [999999.99, 'Cost price cannot exceed 999,999.99'],
    default: 0
  },
  stockQuantity: {
    type: Number,
    required: [true, 'Stock quantity is required'],
    min: [0, 'Stock quantity cannot be negative'],
    max: [999999, 'Stock quantity cannot exceed 999,999'],
    default: 0,
    validate: {
      validator: Number.isInteger,
      message: 'Stock quantity must be a whole number'
    }
  },
  minStockLevel: {
    type: Number,
    min: [0, 'Minimum stock level cannot be negative'],
    default: LOW_STOCK_THRESHOLD,
    validate: {
      validator: Number.isInteger,
      message: 'Minimum stock level must be a whole number'
    }
  },
  maxStockLevel: {
    type: Number,
    min: [0, 'Maximum stock level cannot be negative'],
    validate: {
      validator: Number.isInteger,
      message: 'Maximum stock level must be a whole number'
    }
  },
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: {
      values: PRODUCT_CATEGORIES,
      message: `Category must be one of: ${PRODUCT_CATEGORIES.join(', ')}`
    },
    lowercase: true,
    trim: true
  },
  barcode: {
    type: String,
    trim: true,
    unique: true,
    sparse: true,
    validate: {
      validator: function(value) {
        if (!value) return true;
        return /^[0-9]{8,13}$/.test(value);
      },
      message: 'Barcode must be 8-13 digits'
    }
  },
  sku: {
    type: String,
    trim: true,
    unique: true,
    sparse: true,
    uppercase: true
  },
  supplier: {
    name: {
      type: String,
      trim: true,
      maxlength: [100, 'Supplier name cannot exceed 100 characters']
    },
    contact: {
      type: String,
      trim: true,
      maxlength: [100, 'Supplier contact cannot exceed 100 characters']
    }
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true,
    maxlength: [30, 'Tag cannot exceed 30 characters']
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  isDiscontinued: {
    type: Boolean,
    default: false
  },
  lastRestocked: {
    type: Date,
    default: null
  },
  expiryDate: {
    type: Date,
    validate: {
      validator: function(value) {
        if (!value) return true;
        return value > new Date();
      },
      message: 'Expiry date must be in the future'
    }
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});
