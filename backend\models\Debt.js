const mongoose = require('mongoose');
const validator = require('validator');
const logger = require('../src/config/logger');

// Debt status enum
const DEBT_STATUS = ['pending', 'partial', 'paid', 'overdue', 'cancelled'];

// Payment methods enum
const PAYMENT_METHODS = ['cash', 'gcash', 'paymaya', 'bank_transfer', 'credit', 'other'];

const debtSchema = new mongoose.Schema({
  customerName: {
    type: String,
    required: [true, 'Customer name is required'],
    trim: true,
    minlength: [2, 'Customer name must be at least 2 characters'],
    maxlength: [100, 'Customer name cannot exceed 100 characters'],
    validate: {
      validator: function(value) {
        return /^[a-zA-Z\s\-'.]+$/.test(value);
      },
      message: 'Customer name contains invalid characters'
    },
    index: true
  },
  customerPhone: {
    type: String,
    trim: true,
    validate: {
      validator: function(value) {
        if (!value) return true;
        return /^[\+]?[1-9][\d]{0,15}$/.test(value);
      },
      message: 'Please provide a valid phone number'
    }
  },
  customerEmail: {
    type: String,
    trim: true,
    lowercase: true,
    validate: {
      validator: function(value) {
        if (!value) return true;
        return validator.isEmail(value);
      },
      message: 'Please provide a valid email address'
    }
  },
  productName: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    maxlength: [100, 'Product name cannot exceed 100 characters']
  },
  productId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: [true, 'Product ID is required'],
    index: true
  },
  priceAtTimeOfDebt: {
    type: Number,
    required: [true, 'Price at time of debt is required'],
    min: [0.01, 'Price must be at least 0.01'],
    max: [999999.99, 'Price cannot exceed 999,999.99'],
    validate: {
      validator: function(value) {
        return Number.isFinite(value) && value > 0;
      },
      message: 'Price must be a valid positive number'
    }
  },
  quantity: {
    type: Number,
    required: [true, 'Quantity is required'],
    min: [1, 'Quantity must be at least 1'],
    max: [9999, 'Quantity cannot exceed 9,999'],
    validate: {
      validator: Number.isInteger,
      message: 'Quantity must be a whole number'
    }
  },
  totalAmount: {
    type: Number,
    required: true,
    min: [0.01, 'Total amount must be at least 0.01']
  },
  amountPaid: {
    type: Number,
    default: 0,
    min: [0, 'Amount paid cannot be negative'],
    validate: {
      validator: function(value) {
        return value <= this.totalAmount;
      },
      message: 'Amount paid cannot exceed total amount'
    }
  },
  remainingAmount: {
    type: Number,
    default: function() {
      return this.totalAmount - this.amountPaid;
    }
  },
  dateOfDebt: {
    type: Date,
    required: [true, 'Date of debt is required'],
    default: Date.now,
    index: true
  },
  dueDate: {
    type: Date,
    validate: {
      validator: function(value) {
        if (!value) return true;
        return value >= this.dateOfDebt;
      },
      message: 'Due date must be after or equal to debt date'
    }
  },
  status: {
    type: String,
    enum: {
      values: DEBT_STATUS,
      message: `Status must be one of: ${DEBT_STATUS.join(', ')}`
    },
    default: 'pending',
    index: true
  },
  isPaid: {
    type: Boolean,
    default: false,
    index: true
  },
  datePaid: {
    type: Date,
    default: null
  },
  paymentMethod: {
    type: String,
    enum: {
      values: PAYMENT_METHODS,
      message: `Payment method must be one of: ${PAYMENT_METHODS.join(', ')}`
    }
  },
  paymentReference: {
    type: String,
    trim: true,
    maxlength: [100, 'Payment reference cannot exceed 100 characters']
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [500, 'Notes cannot exceed 500 characters'],
    default: ''
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true,
    maxlength: [30, 'Tag cannot exceed 30 characters']
  }],
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  payments: [{
    amount: {
      type: Number,
      required: true,
      min: [0.01, 'Payment amount must be at least 0.01']
    },
    method: {
      type: String,
      enum: PAYMENT_METHODS,
      required: true
    },
    reference: {
      type: String,
      trim: true
    },
    date: {
      type: Date,
      default: Date.now
    },
    notes: {
      type: String,
      trim: true,
      maxlength: [200, 'Payment notes cannot exceed 200 characters']
    },
    recordedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    }
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
debtSchema.index({ customerName: 1, status: 1 });
debtSchema.index({ customerName: 1, isPaid: 1 });
debtSchema.index({ dateOfDebt: -1 });
debtSchema.index({ dueDate: 1 });
debtSchema.index({ productId: 1 });
debtSchema.index({ status: 1, dueDate: 1 });
debtSchema.index({ createdBy: 1 });
debtSchema.index({ createdAt: -1 });

// Virtual fields
debtSchema.virtual('isOverdue').get(function() {
  if (!this.dueDate || this.isPaid) return false;
  return new Date() > this.dueDate;
});

debtSchema.virtual('daysOverdue').get(function() {
  if (!this.isOverdue) return 0;
  const diffTime = Math.abs(new Date() - this.dueDate);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

debtSchema.virtual('paymentProgress').get(function() {
  if (this.totalAmount === 0) return 100;
  return Math.round((this.amountPaid / this.totalAmount) * 100);
});

debtSchema.virtual('customerDebtSummary', {
  ref: 'Debt',
  localField: 'customerName',
  foreignField: 'customerName'
});

// Pre-save middleware
debtSchema.pre('save', function(next) {
  // Calculate total amount
  this.totalAmount = Math.round((this.priceAtTimeOfDebt * this.quantity) * 100) / 100;

  // Calculate remaining amount
  this.remainingAmount = Math.round((this.totalAmount - this.amountPaid) * 100) / 100;

  // Update status based on payment
  if (this.amountPaid === 0) {
    this.status = this.isOverdue ? 'overdue' : 'pending';
    this.isPaid = false;
  } else if (this.amountPaid >= this.totalAmount) {
    this.status = 'paid';
    this.isPaid = true;
    if (!this.datePaid) {
      this.datePaid = new Date();
    }
  } else {
    this.status = 'partial';
    this.isPaid = false;
    this.datePaid = null;
  }

  // Set due date if not provided (default 30 days)
  if (!this.dueDate && this.isNew) {
    this.dueDate = new Date(this.dateOfDebt.getTime() + (30 * 24 * 60 * 60 * 1000));
  }

  next();
});

// Post-save middleware for logging
debtSchema.post('save', function(doc) {
  logger.logDatabase('save', 'debts', { id: doc._id }, { acknowledged: true });
});

// Instance methods
debtSchema.methods.addPayment = function(amount, method, reference, notes, recordedBy) {
  if (amount <= 0) {
    throw new Error('Payment amount must be positive');
  }

  if (this.amountPaid + amount > this.totalAmount) {
    throw new Error('Payment amount exceeds remaining debt');
  }

  this.payments.push({
    amount,
    method,
    reference,
    notes,
    recordedBy,
    date: new Date()
  });

  this.amountPaid += amount;
  return this.save();
};

debtSchema.methods.markAsPaid = function(method, reference, recordedBy) {
  const remainingAmount = this.totalAmount - this.amountPaid;

  if (remainingAmount > 0) {
    return this.addPayment(remainingAmount, method, reference, 'Final payment', recordedBy);
  }

  this.isPaid = true;
  this.status = 'paid';
  this.datePaid = new Date();
  return this.save();
};

debtSchema.methods.cancel = function(reason, cancelledBy) {
  this.status = 'cancelled';
  this.notes = this.notes ? `${this.notes}\nCancelled: ${reason}` : `Cancelled: ${reason}`;
  this.updatedBy = cancelledBy;
  return this.save();
};

debtSchema.methods.extendDueDate = function(newDueDate, reason, updatedBy) {
  if (newDueDate <= this.dueDate) {
    throw new Error('New due date must be after current due date');
  }

  this.dueDate = newDueDate;
  this.notes = this.notes ? `${this.notes}\nDue date extended: ${reason}` : `Due date extended: ${reason}`;
  this.updatedBy = updatedBy;
  return this.save();
};

// Static methods
debtSchema.statics.findByCustomer = function(customerName, options = {}) {
  const { includeInactive = false, sortBy = { dateOfDebt: -1 } } = options;

  const filter = {
    customerName: { $regex: new RegExp(customerName, 'i') }
  };

  if (!includeInactive) {
    filter.status = { $ne: 'cancelled' };
  }

  return this.find(filter).sort(sortBy);
};

debtSchema.statics.findOverdue = function() {
  return this.find({
    dueDate: { $lt: new Date() },
    isPaid: false,
    status: { $ne: 'cancelled' }
  }).sort({ dueDate: 1 });
};

debtSchema.statics.findUnpaid = function() {
  return this.find({
    isPaid: false,
    status: { $ne: 'cancelled' }
  }).sort({ dateOfDebt: -1 });
};

debtSchema.statics.getCustomerSummary = function(customerName) {
  return this.aggregate([
    {
      $match: {
        customerName: { $regex: new RegExp(customerName, 'i') },
        status: { $ne: 'cancelled' }
      }
    },
    {
      $group: {
        _id: '$customerName',
        totalDebt: { $sum: '$totalAmount' },
        totalPaid: { $sum: '$amountPaid' },
        totalUnpaid: { $sum: '$remainingAmount' },
        debtCount: { $sum: 1 },
        unpaidCount: {
          $sum: { $cond: [{ $eq: ['$isPaid', false] }, 1, 0] }
        },
        overdueCount: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $lt: ['$dueDate', new Date()] },
                  { $eq: ['$isPaid', false] }
                ]
              },
              1,
              0
            ]
          }
        },
        lastDebtDate: { $max: '$dateOfDebt' },
        oldestUnpaidDate: {
          $min: {
            $cond: [{ $eq: ['$isPaid', false] }, '$dateOfDebt', null]
          }
        }
      }
    }
  ]);
};

debtSchema.statics.getDebtStatistics = function() {
  return this.aggregate([
    {
      $match: { status: { $ne: 'cancelled' } }
    },
    {
      $group: {
        _id: null,
        totalDebts: { $sum: 1 },
        totalAmount: { $sum: '$totalAmount' },
        totalPaid: { $sum: '$amountPaid' },
        totalUnpaid: { $sum: '$remainingAmount' },
        unpaidDebts: {
          $sum: { $cond: [{ $eq: ['$isPaid', false] }, 1, 0] }
        },
        overdueDebts: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $lt: ['$dueDate', new Date()] },
                  { $eq: ['$isPaid', false] }
                ]
              },
              1,
              0
            ]
          }
        },
        uniqueCustomers: { $addToSet: '$customerName' }
      }
    },
    {
      $addFields: {
        customerCount: { $size: '$uniqueCustomers' }
      }
    },
    {
      $project: {
        uniqueCustomers: 0
      }
    }
  ]);
};

module.exports = mongoose.model('Debt', debtSchema);
