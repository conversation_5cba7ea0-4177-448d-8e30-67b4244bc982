const express = require('express');
const { body, validationResult, param, query } = require('express-validator');
const Debt = require('../models/Debt');
const Product = require('../models/Product');
const { catchAsync, handleValidationError } = require('../src/middleware/errorHandler');
const logger = require('../src/config/logger');

const router = express.Router();

// Validation rules
const debtValidation = [
  body('customerName')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Customer name must be between 2 and 100 characters')
    .matches(/^[a-zA-Z\s\-'.]+$/)
    .withMessage('Customer name contains invalid characters'),
  body('customerPhone')
    .optional()
    .trim()
    .matches(/^[\+]?[1-9][\d]{0,15}$/)
    .withMessage('Please provide a valid phone number'),
  body('customerEmail')
    .optional()
    .trim()
    .isEmail()
    .withMessage('Please provide a valid email address'),
  body('productId')
    .isMongoId()
    .withMessage('Invalid product ID'),
  body('quantity')
    .isInt({ min: 1, max: 9999 })
    .withMessage('Quantity must be between 1 and 9,999'),
  body('dateOfDebt')
    .optional()
    .isISO8601()
    .withMessage('Invalid date format')
    .custom((value) => {
      if (new Date(value) > new Date()) {
        throw new Error('Date of debt cannot be in the future');
      }
      return true;
    }),
  body('dueDate')
    .optional()
    .isISO8601()
    .withMessage('Invalid due date format')
    .custom((value, { req }) => {
      const debtDate = new Date(req.body.dateOfDebt || Date.now());
      if (new Date(value) < debtDate) {
        throw new Error('Due date must be after or equal to debt date');
      }
      return true;
    }),
  body('notes')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Notes cannot exceed 500 characters'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Priority must be low, medium, high, or urgent'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('tags.*')
    .optional()
    .trim()
    .isLength({ max: 30 })
    .withMessage('Each tag cannot exceed 30 characters')
];

// Validation middleware
const validateDebt = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation errors',
      errors: errors.array().map(error => ({
        field: error.path || error.param,
        message: error.msg,
        value: error.value
      }))
    });
  }
  next();
};

// GET /api/debts - Get all debts with optional filtering
router.get('/', [
  query('customerName').optional().isString(),
  query('isPaid').optional().isBoolean(),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 })
], async (req, res) => {
  try {
    const { customerName, isPaid, page = 1, limit = 10 } = req.query;
    const skip = (page - 1) * limit;
    
    let filter = {};
    
    if (customerName) {
      filter.customerName = { $regex: customerName, $options: 'i' };
    }
    
    if (isPaid !== undefined) {
      filter.isPaid = isPaid === 'true';
    }
    
    const debts = await Debt.find(filter)
      .populate('productId', 'name category')
      .sort({ dateOfDebt: -1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    const total = await Debt.countDocuments(filter);
    
    res.json({
      success: true,
      data: debts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching debts',
      error: error.message
    });
  }
});

// GET /api/debts/:id - Get single debt
router.get('/:id', [
  param('id').isMongoId().withMessage('Invalid debt ID')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }
    
    const debt = await Debt.findById(req.params.id)
      .populate('productId', 'name category price');
    
    if (!debt) {
      return res.status(404).json({
        success: false,
        message: 'Debt not found'
      });
    }
    
    res.json({
      success: true,
      data: debt
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching debt',
      error: error.message
    });
  }
});

// GET /api/debts/customer/:name - Get debts by customer name
router.get('/customer/:name', [
  param('name').trim().isLength({ min: 1 }).withMessage('Customer name is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }
    
    const customerName = req.params.name;
    
    const debts = await Debt.find({ 
      customerName: { $regex: customerName, $options: 'i' } 
    })
      .populate('productId', 'name category')
      .sort({ dateOfDebt: -1 });
    
    // Calculate totals
    const totalDebt = debts.reduce((sum, debt) => sum + debt.totalAmount, 0);
    const unpaidDebt = debts
      .filter(debt => !debt.isPaid)
      .reduce((sum, debt) => sum + debt.totalAmount, 0);
    const paidDebt = totalDebt - unpaidDebt;
    
    res.json({
      success: true,
      data: {
        customerName,
        debts,
        summary: {
          totalDebt,
          unpaidDebt,
          paidDebt,
          totalRecords: debts.length,
          unpaidRecords: debts.filter(debt => !debt.isPaid).length
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching customer debts',
      error: error.message
    });
  }
});

// GET /api/debts/summary/customers - Get debt summary for all customers
router.get('/summary/customers', async (req, res) => {
  try {
    const summary = await Debt.aggregate([
      {
        $group: {
          _id: '$customerName',
          totalDebt: { $sum: '$totalAmount' },
          unpaidDebt: {
            $sum: {
              $cond: [{ $eq: ['$isPaid', false] }, '$totalAmount', 0]
            }
          },
          totalRecords: { $sum: 1 },
          unpaidRecords: {
            $sum: {
              $cond: [{ $eq: ['$isPaid', false] }, 1, 0]
            }
          },
          lastDebtDate: { $max: '$dateOfDebt' }
        }
      },
      {
        $addFields: {
          paidDebt: { $subtract: ['$totalDebt', '$unpaidDebt'] }
        }
      },
      {
        $sort: { unpaidDebt: -1, totalDebt: -1 }
      }
    ]);
    
    res.json({
      success: true,
      data: summary
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching customer debt summary',
      error: error.message
    });
  }
});

// POST /api/debts - Create new debt record
router.post('/', debtValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    // Verify product exists
    const product = await Product.findById(req.body.productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    const debtData = {
      customerName: req.body.customerName,
      productName: product.name,
      productId: req.body.productId,
      priceAtTimeOfDebt: product.price,
      quantity: parseInt(req.body.quantity),
      dateOfDebt: req.body.dateOfDebt || new Date(),
      notes: req.body.notes || ''
    };

    const debt = new Debt(debtData);
    await debt.save();

    // Populate product details for response
    await debt.populate('productId', 'name category');

    res.status(201).json({
      success: true,
      message: 'Debt record created successfully',
      data: debt
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error creating debt record',
      error: error.message
    });
  }
});

// PUT /api/debts/:id - Update debt record
router.put('/:id', [
  param('id').isMongoId().withMessage('Invalid debt ID'),
  ...debtValidation,
  body('isPaid').optional().isBoolean().withMessage('isPaid must be a boolean')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const debt = await Debt.findById(req.params.id);
    if (!debt) {
      return res.status(404).json({
        success: false,
        message: 'Debt not found'
      });
    }

    // Verify product exists if productId is being updated
    if (req.body.productId && req.body.productId !== debt.productId.toString()) {
      const product = await Product.findById(req.body.productId);
      if (!product) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }
      debt.productName = product.name;
      debt.priceAtTimeOfDebt = product.price;
    }

    // Update fields
    debt.customerName = req.body.customerName;
    debt.productId = req.body.productId;
    debt.quantity = parseInt(req.body.quantity);
    debt.dateOfDebt = req.body.dateOfDebt || debt.dateOfDebt;
    debt.notes = req.body.notes || debt.notes;

    if (req.body.isPaid !== undefined) {
      debt.isPaid = req.body.isPaid;
    }

    await debt.save();
    await debt.populate('productId', 'name category');

    res.json({
      success: true,
      message: 'Debt record updated successfully',
      data: debt
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error updating debt record',
      error: error.message
    });
  }
});

// PATCH /api/debts/:id/payment - Mark debt as paid/unpaid
router.patch('/:id/payment', [
  param('id').isMongoId().withMessage('Invalid debt ID'),
  body('isPaid').isBoolean().withMessage('isPaid must be a boolean')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const debt = await Debt.findByIdAndUpdate(
      req.params.id,
      { isPaid: req.body.isPaid },
      { new: true, runValidators: true }
    ).populate('productId', 'name category');

    if (!debt) {
      return res.status(404).json({
        success: false,
        message: 'Debt not found'
      });
    }

    res.json({
      success: true,
      message: `Debt marked as ${req.body.isPaid ? 'paid' : 'unpaid'}`,
      data: debt
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error updating payment status',
      error: error.message
    });
  }
});

// DELETE /api/debts/:id - Delete debt record
router.delete('/:id', [
  param('id').isMongoId().withMessage('Invalid debt ID')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const debt = await Debt.findByIdAndDelete(req.params.id);

    if (!debt) {
      return res.status(404).json({
        success: false,
        message: 'Debt not found'
      });
    }

    res.json({
      success: true,
      message: 'Debt record deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error deleting debt record',
      error: error.message
    });
  }
});

module.exports = router;
