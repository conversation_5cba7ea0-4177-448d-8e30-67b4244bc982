const redis = require('redis');
const logger = require('../config/logger');

/**
 * Cache utility with Redis support and in-memory fallback
 */
class CacheManager {
  constructor() {
    this.redisClient = null;
    this.memoryCache = new Map();
    this.isRedisConnected = false;
    this.maxMemoryCacheSize = 1000;
    this.defaultTTL = 300; // 5 minutes
    
    this.initializeRedis();
  }

  /**
   * Initialize Redis connection
   */
  async initializeRedis() {
    if (!process.env.REDIS_URL) {
      logger.info('Redis URL not configured, using in-memory cache only');
      return;
    }

    try {
      this.redisClient = redis.createClient({
        url: process.env.REDIS_URL,
        password: process.env.REDIS_PASSWORD,
        database: parseInt(process.env.REDIS_DB) || 0,
        retry_strategy: (options) => {
          if (options.error && options.error.code === 'ECONNREFUSED') {
            logger.error('Redis server connection refused');
            return new Error('Redis server connection refused');
          }
          if (options.total_retry_time > 1000 * 60 * 60) {
            logger.error('Redis retry time exhausted');
            return new Error('Retry time exhausted');
          }
          if (options.attempt > 10) {
            logger.error('Redis max retry attempts reached');
            return undefined;
          }
          return Math.min(options.attempt * 100, 3000);
        }
      });

      this.redisClient.on('connect', () => {
        logger.info('Redis client connected');
        this.isRedisConnected = true;
      });

      this.redisClient.on('error', (err) => {
        logger.error('Redis client error:', err);
        this.isRedisConnected = false;
      });

      this.redisClient.on('end', () => {
        logger.warn('Redis client disconnected');
        this.isRedisConnected = false;
      });

      await this.redisClient.connect();
    } catch (error) {
      logger.error('Failed to initialize Redis:', error);
      this.isRedisConnected = false;
    }
  }

  /**
   * Get value from cache
   */
  async get(key) {
    try {
      // Try Redis first
      if (this.isRedisConnected && this.redisClient) {
        const value = await this.redisClient.get(key);
        if (value !== null) {
          logger.debug('Cache hit (Redis)', { key });
          return JSON.parse(value);
        }
      }

      // Fallback to memory cache
      const memoryValue = this.memoryCache.get(key);
      if (memoryValue) {
        // Check if expired
        if (memoryValue.expiry && Date.now() > memoryValue.expiry) {
          this.memoryCache.delete(key);
          logger.debug('Cache expired (Memory)', { key });
          return null;
        }
        logger.debug('Cache hit (Memory)', { key });
        return memoryValue.data;
      }

      logger.debug('Cache miss', { key });
      return null;
    } catch (error) {
      logger.error('Cache get error:', error);
      return null;
    }
  }

  /**
   * Set value in cache
   */
  async set(key, value, ttl = this.defaultTTL) {
    try {
      const serializedValue = JSON.stringify(value);

      // Try Redis first
      if (this.isRedisConnected && this.redisClient) {
        await this.redisClient.setEx(key, ttl, serializedValue);
        logger.debug('Cache set (Redis)', { key, ttl });
      }

      // Always set in memory cache as fallback
      this.setMemoryCache(key, value, ttl);
      logger.debug('Cache set (Memory)', { key, ttl });

      return true;
    } catch (error) {
      logger.error('Cache set error:', error);
      return false;
    }
  }

  /**
   * Set value in memory cache
   */
  setMemoryCache(key, value, ttl) {
    // Clean up expired entries if cache is getting large
    if (this.memoryCache.size >= this.maxMemoryCacheSize) {
      this.cleanupMemoryCache();
    }

    const expiry = ttl > 0 ? Date.now() + (ttl * 1000) : null;
    this.memoryCache.set(key, { data: value, expiry });
  }

  /**
   * Delete value from cache
   */
  async del(key) {
    try {
      // Delete from Redis
      if (this.isRedisConnected && this.redisClient) {
        await this.redisClient.del(key);
      }

      // Delete from memory cache
      this.memoryCache.delete(key);
      
      logger.debug('Cache deleted', { key });
      return true;
    } catch (error) {
      logger.error('Cache delete error:', error);
      return false;
    }
  }

  /**
   * Clear all cache
   */
  async clear() {
    try {
      // Clear Redis
      if (this.isRedisConnected && this.redisClient) {
        await this.redisClient.flushDb();
      }

      // Clear memory cache
      this.memoryCache.clear();
      
      logger.info('Cache cleared');
      return true;
    } catch (error) {
      logger.error('Cache clear error:', error);
      return false;
    }
  }

  /**
   * Get or set pattern (cache-aside)
   */
  async getOrSet(key, fetchFunction, ttl = this.defaultTTL) {
    try {
      // Try to get from cache first
      let value = await this.get(key);
      
      if (value !== null) {
        return value;
      }

      // If not in cache, fetch the data
      logger.debug('Cache miss, fetching data', { key });
      value = await fetchFunction();

      // Store in cache
      if (value !== null && value !== undefined) {
        await this.set(key, value, ttl);
      }

      return value;
    } catch (error) {
      logger.error('Cache getOrSet error:', error);
      // If cache fails, still try to fetch the data
      return await fetchFunction();
    }
  }

  /**
   * Cleanup expired entries from memory cache
   */
  cleanupMemoryCache() {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, value] of this.memoryCache.entries()) {
      if (value.expiry && now > value.expiry) {
        this.memoryCache.delete(key);
        cleanedCount++;
      }
    }

    // If still too large, remove oldest entries
    if (this.memoryCache.size >= this.maxMemoryCacheSize) {
      const entries = Array.from(this.memoryCache.entries());
      const toRemove = entries.slice(0, Math.floor(this.maxMemoryCacheSize * 0.2));
      
      toRemove.forEach(([key]) => {
        this.memoryCache.delete(key);
        cleanedCount++;
      });
    }

    if (cleanedCount > 0) {
      logger.debug('Memory cache cleanup completed', { 
        cleanedCount, 
        remainingSize: this.memoryCache.size 
      });
    }
  }

  /**
   * Get cache statistics
   */
  async getStats() {
    const stats = {
      memoryCache: {
        size: this.memoryCache.size,
        maxSize: this.maxMemoryCacheSize
      },
      redis: {
        connected: this.isRedisConnected
      }
    };

    if (this.isRedisConnected && this.redisClient) {
      try {
        const info = await this.redisClient.info('memory');
        const keyspace = await this.redisClient.info('keyspace');
        
        stats.redis.memory = info;
        stats.redis.keyspace = keyspace;
      } catch (error) {
        logger.error('Failed to get Redis stats:', error);
      }
    }

    return stats;
  }

  /**
   * Generate cache key
   */
  generateKey(prefix, ...parts) {
    return `${prefix}:${parts.join(':')}`;
  }

  /**
   * Cache middleware for Express routes
   */
  middleware(keyGenerator, ttl = this.defaultTTL) {
    return async (req, res, next) => {
      try {
        const key = typeof keyGenerator === 'function' 
          ? keyGenerator(req) 
          : keyGenerator;

        const cachedData = await this.get(key);
        
        if (cachedData) {
          logger.debug('Serving from cache', { key, path: req.path });
          return res.json(cachedData);
        }

        // Store original res.json
        const originalJson = res.json;
        
        // Override res.json to cache the response
        res.json = function(data) {
          // Only cache successful responses
          if (res.statusCode === 200 && data && data.success !== false) {
            cache.set(key, data, ttl).catch(err => {
              logger.error('Failed to cache response:', err);
            });
          }
          
          return originalJson.call(this, data);
        };

        next();
      } catch (error) {
        logger.error('Cache middleware error:', error);
        next();
      }
    };
  }

  /**
   * Invalidate cache by pattern
   */
  async invalidatePattern(pattern) {
    try {
      if (this.isRedisConnected && this.redisClient) {
        const keys = await this.redisClient.keys(pattern);
        if (keys.length > 0) {
          await this.redisClient.del(keys);
          logger.debug('Invalidated cache pattern (Redis)', { pattern, count: keys.length });
        }
      }

      // Invalidate memory cache
      let count = 0;
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      
      for (const key of this.memoryCache.keys()) {
        if (regex.test(key)) {
          this.memoryCache.delete(key);
          count++;
        }
      }

      if (count > 0) {
        logger.debug('Invalidated cache pattern (Memory)', { pattern, count });
      }

      return true;
    } catch (error) {
      logger.error('Cache pattern invalidation error:', error);
      return false;
    }
  }

  /**
   * Close connections
   */
  async close() {
    try {
      if (this.redisClient) {
        await this.redisClient.quit();
      }
      this.memoryCache.clear();
      logger.info('Cache connections closed');
    } catch (error) {
      logger.error('Error closing cache connections:', error);
    }
  }
}

// Create singleton instance
const cache = new CacheManager();

// Cache key generators
const CacheKeys = {
  product: (id) => cache.generateKey('product', id),
  products: (filters) => cache.generateKey('products', JSON.stringify(filters)),
  productStats: () => cache.generateKey('product', 'stats'),
  productCategories: () => cache.generateKey('product', 'categories'),
  debt: (id) => cache.generateKey('debt', id),
  debts: (filters) => cache.generateKey('debts', JSON.stringify(filters)),
  debtStats: () => cache.generateKey('debt', 'stats'),
  customerSummary: (name) => cache.generateKey('customer', 'summary', name),
  user: (id) => cache.generateKey('user', id),
  health: () => cache.generateKey('system', 'health')
};

module.exports = {
  cache,
  CacheKeys
};
