# Sari-Sari Store API - Development Guide

## Table of Contents

1. [Getting Started](#getting-started)
2. [Project Structure](#project-structure)
3. [Development Workflow](#development-workflow)
4. [Code Standards](#code-standards)
5. [Testing](#testing)
6. [Database](#database)
7. [Performance](#performance)
8. [Security](#security)
9. [Deployment](#deployment)
10. [Troubleshooting](#troubleshooting)

## Getting Started

### Prerequisites

- Node.js 18+ 
- MongoDB 5.0+
- Redis (optional, for caching)
- Git

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd sari-sari-store/backend

# Install dependencies
npm install

# Copy environment variables
cp .env.example .env

# Edit environment variables
nano .env

# Run database migrations
npm run migrate

# Seed the database (optional)
npm run seed

# Start development server
npm run dev
```

### Environment Variables

Create a `.env` file with the following variables:

```env
# Server Configuration
NODE_ENV=development
PORT=5000
API_VERSION=v1

# Database
MONGODB_URI=mongodb://localhost:27017/sari-sari-store
MONGODB_TEST_URI=mongodb://localhost:27017/sari-sari-store-test

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=15m
JWT_REFRESH_SECRET=your-refresh-secret
JWT_REFRESH_EXPIRES_IN=7d

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=jpeg,jpg,png,gif,webp

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# CORS
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# Redis (optional)
REDIS_URL=redis://localhost:6379
```

## Project Structure

```
backend/
├── docs/                   # Documentation
├── logs/                   # Application logs
├── models/                 # Mongoose models
├── routes/                 # Express routes
├── scripts/                # Utility scripts
├── src/
│   ├── config/            # Configuration files
│   ├── middleware/        # Express middleware
│   └── utils/             # Utility functions
├── tests/                 # Test files
├── uploads/               # File uploads
├── .env                   # Environment variables
├── .eslintrc.js          # ESLint configuration
├── .prettierrc.js        # Prettier configuration
├── jest.config.js        # Jest configuration
├── package.json          # Dependencies and scripts
└── server.js             # Application entry point
```

## Development Workflow

### 1. Feature Development

```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes
# Write tests
# Update documentation

# Run tests
npm test

# Run linting
npm run lint

# Format code
npm run format

# Commit changes
git add .
git commit -m "feat: add new feature"

# Push and create PR
git push origin feature/new-feature
```

### 2. Code Review Checklist

- [ ] Code follows style guidelines
- [ ] Tests are written and passing
- [ ] Documentation is updated
- [ ] No security vulnerabilities
- [ ] Performance considerations addressed
- [ ] Error handling implemented
- [ ] Logging added where appropriate

### 3. Available Scripts

```bash
# Development
npm run dev              # Start development server with nodemon
npm start               # Start production server

# Testing
npm test                # Run all tests
npm run test:watch      # Run tests in watch mode
npm run test:coverage   # Run tests with coverage
npm run test:unit       # Run unit tests only
npm run test:integration # Run integration tests only

# Code Quality
npm run lint            # Run ESLint
npm run lint:fix        # Fix ESLint issues
npm run format          # Format code with Prettier
npm run format:check    # Check code formatting
npm run validate        # Run linting and tests

# Database
npm run seed            # Seed database with sample data
npm run migrate         # Run database migrations
npm run db:analyze      # Analyze database performance
npm run db:indexes      # Show database indexes
npm run db:stats        # Show database statistics
```

## Code Standards

### 1. JavaScript Style Guide

- Use ES6+ features
- Prefer `const` over `let`, avoid `var`
- Use arrow functions for callbacks
- Use template literals for string interpolation
- Use destructuring for object/array extraction
- Use async/await over Promises when possible

### 2. Naming Conventions

- **Variables/Functions**: camelCase (`getUserById`)
- **Constants**: UPPER_SNAKE_CASE (`MAX_RETRY_ATTEMPTS`)
- **Classes**: PascalCase (`UserService`)
- **Files**: kebab-case (`user-service.js`)
- **Database Collections**: lowercase plural (`users`, `products`)

### 3. Error Handling

```javascript
// Use try-catch for async operations
const createUser = catchAsync(async (req, res) => {
  try {
    const user = await User.create(req.body);
    res.status(201).json({ success: true, data: user });
  } catch (error) {
    throw new AppError('Failed to create user', 400);
  }
});

// Use proper error types
throw new AppError('User not found', 404, 'USER_NOT_FOUND');
```

### 4. Logging

```javascript
// Use structured logging
logger.info('User created successfully', {
  userId: user._id,
  email: user.email,
  timestamp: new Date().toISOString()
});

// Log errors with context
logger.error('Database connection failed', {
  error: error.message,
  stack: error.stack,
  operation: 'user-creation'
});
```

### 5. API Response Format

```javascript
// Success response
{
  "success": true,
  "data": { ... },
  "pagination": { ... }, // if applicable
  "message": "Operation completed successfully"
}

// Error response
{
  "success": false,
  "error": {
    "name": "ValidationError",
    "message": "Validation failed",
    "code": "VALIDATION_FAILED",
    "errors": [...],
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

## Testing

### 1. Test Structure

```javascript
describe('User Model', () => {
  beforeAll(async () => {
    await setupTestDB();
  });

  afterAll(async () => {
    await teardownTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();
  });

  describe('User Creation', () => {
    it('should create a valid user', async () => {
      const userData = createTestUser();
      const user = new User(userData);
      const savedUser = await user.save();

      expect(savedUser._id).toBeDefined();
      expect(savedUser.email).toBe(userData.email);
    });
  });
});
```

### 2. Test Categories

- **Unit Tests**: Test individual functions/methods
- **Integration Tests**: Test API endpoints
- **E2E Tests**: Test complete user workflows

### 3. Test Coverage

Maintain minimum 80% test coverage:

```bash
npm run test:coverage
```

## Database

### 1. Schema Design

- Use proper data types
- Add validation rules
- Create appropriate indexes
- Use virtual fields for computed properties
- Implement middleware for business logic

### 2. Query Optimization

```javascript
// Use indexes effectively
const products = await Product.find({ category: 'snacks', isActive: true })
  .sort({ createdAt: -1 })
  .limit(10)
  .select('name price stockQuantity');

// Use aggregation for complex queries
const stats = await Product.aggregate([
  { $match: { isActive: true } },
  { $group: { _id: '$category', count: { $sum: 1 } } }
]);
```

### 3. Migrations

Run migrations before deploying:

```bash
npm run migrate
```

## Performance

### 1. Caching Strategy

- Use Redis for frequently accessed data
- Implement cache invalidation
- Cache API responses for read-heavy operations

### 2. Database Optimization

- Create proper indexes
- Use query optimization
- Implement pagination
- Use aggregation pipelines efficiently

### 3. Monitoring

- Monitor response times
- Track error rates
- Monitor memory usage
- Set up alerts for critical metrics

## Security

### 1. Authentication & Authorization

- Use JWT tokens
- Implement refresh tokens
- Use role-based access control
- Validate all inputs

### 2. Data Protection

- Hash passwords with bcrypt
- Sanitize user inputs
- Use HTTPS in production
- Implement rate limiting

### 3. Security Headers

- Use Helmet.js
- Implement CORS properly
- Set security headers
- Validate file uploads

## Deployment

### 1. Production Checklist

- [ ] Environment variables configured
- [ ] Database migrations run
- [ ] SSL certificates installed
- [ ] Monitoring set up
- [ ] Backup strategy implemented
- [ ] Error tracking configured

### 2. Environment Setup

```bash
# Production environment
NODE_ENV=production
LOG_LEVEL=warn
MONGODB_URI=mongodb://production-server/database
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check MongoDB service status
   - Verify connection string
   - Check network connectivity

2. **High Memory Usage**
   - Check for memory leaks
   - Monitor query performance
   - Review caching strategy

3. **Slow API Responses**
   - Check database indexes
   - Review query optimization
   - Monitor cache hit rates

### Debug Mode

```bash
DEBUG=sari-sari:* npm run dev
```

### Logs

Check application logs:

```bash
tail -f logs/app.log
```

## Contributing

1. Follow the development workflow
2. Write tests for new features
3. Update documentation
4. Follow code standards
5. Submit pull requests for review

For more information, see the [API Documentation](./API.md).
