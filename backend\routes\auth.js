const express = require('express');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');
const User = require('../models/User');
const { authenticate, optionalAuth } = require('../src/middleware/auth');
const { catchAsync, AppError } = require('../src/middleware/errorHandler');
const logger = require('../src/config/logger');

const router = express.Router();

// Rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: {
    success: false,
    message: 'Too many authentication attempts. Please try again later.',
    retryAfter: 15 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true
});

// Validation rules
const registerValidation = [
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s\-']+$/)
    .withMessage('First name contains invalid characters'),
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s\-']+$/)
    .withMessage('Last name contains invalid characters'),
  body('username')
    .trim()
    .isLength({ min: 3, max: 30 })
    .withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('Password confirmation does not match password');
      }
      return true;
    })
];

const loginValidation = [
  body('login')
    .trim()
    .notEmpty()
    .withMessage('Username or email is required'),
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

const forgotPasswordValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email')
];

const resetPasswordValidation = [
  body('token')
    .notEmpty()
    .withMessage('Reset token is required'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
];

// Validation middleware
const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation errors',
      errors: errors.array().map(error => ({
        field: error.path || error.param,
        message: error.msg,
        value: error.value
      }))
    });
  }
  next();
};

// POST /api/auth/register - Register new user
router.post('/register', authLimiter, registerValidation, validateRequest, catchAsync(async (req, res) => {
  const { firstName, lastName, username, email, password, phone } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({
    $or: [{ email }, { username }]
  });

  if (existingUser) {
    const field = existingUser.email === email ? 'email' : 'username';
    return res.status(400).json({
      success: false,
      message: `User with this ${field} already exists`,
      code: 'USER_EXISTS'
    });
  }

  // Create new user
  const user = new User({
    firstName,
    lastName,
    username,
    email,
    password,
    phone,
    role: 'user' // Default role
  });

  await user.save();

  // Generate tokens
  const accessToken = user.generateAccessToken();
  const refreshToken = user.generateRefreshToken();
  
  // Save refresh token
  await user.save();

  // Remove password from response
  user.password = undefined;

  logger.info('User registered successfully', {
    userId: user._id,
    email: user.email,
    username: user.username
  });

  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    data: {
      user,
      tokens: {
        accessToken,
        refreshToken,
        expiresIn: process.env.JWT_EXPIRES_IN || '15m'
      }
    }
  });
}));

// POST /api/auth/login - User login
router.post('/login', authLimiter, loginValidation, validateRequest, catchAsync(async (req, res) => {
  const { login, password } = req.body;
  const ipAddress = req.ip;
  const userAgent = req.get('User-Agent');

  // Find user by email or username
  const user = await User.findOne({
    $or: [
      { email: login.toLowerCase() },
      { username: login.toLowerCase() }
    ]
  }).select('+password');

  if (!user) {
    logger.warn('Login attempt with non-existent user', {
      login,
      ipAddress,
      userAgent
    });
    
    return res.status(401).json({
      success: false,
      message: 'Invalid credentials',
      code: 'INVALID_CREDENTIALS'
    });
  }

  // Check if account is locked
  if (user.isLocked) {
    logger.warn('Login attempt on locked account', {
      userId: user._id,
      ipAddress,
      userAgent
    });
    
    return res.status(423).json({
      success: false,
      message: 'Account is temporarily locked due to multiple failed login attempts',
      code: 'ACCOUNT_LOCKED'
    });
  }

  // Check if account is active
  if (!user.isActive) {
    logger.warn('Login attempt on inactive account', {
      userId: user._id,
      ipAddress,
      userAgent
    });
    
    return res.status(401).json({
      success: false,
      message: 'Account is deactivated',
      code: 'ACCOUNT_DEACTIVATED'
    });
  }

  // Verify password
  const isPasswordValid = await user.comparePassword(password);
  
  if (!isPasswordValid) {
    // Record failed login attempt
    user.recordLogin(ipAddress, userAgent, false);
    await user.save();
    
    logger.warn('Failed login attempt', {
      userId: user._id,
      ipAddress,
      userAgent,
      attempts: user.loginAttempts
    });
    
    return res.status(401).json({
      success: false,
      message: 'Invalid credentials',
      code: 'INVALID_CREDENTIALS'
    });
  }

  // Successful login
  user.recordLogin(ipAddress, userAgent, true);
  user.cleanupExpiredTokens();

  // Generate tokens
  const accessToken = user.generateAccessToken();
  const refreshToken = user.generateRefreshToken();
  
  await user.save();

  // Remove password from response
  user.password = undefined;

  logger.info('User logged in successfully', {
    userId: user._id,
    email: user.email,
    ipAddress,
    userAgent
  });

  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user,
      tokens: {
        accessToken,
        refreshToken,
        expiresIn: process.env.JWT_EXPIRES_IN || '15m'
      }
    }
  });
}));

// POST /api/auth/refresh - Refresh access token
router.post('/refresh', catchAsync(async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    return res.status(400).json({
      success: false,
      message: 'Refresh token is required',
      code: 'REFRESH_TOKEN_REQUIRED'
    });
  }

  try {
    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
    
    if (decoded.type !== 'refresh') {
      return res.status(400).json({
        success: false,
        message: 'Invalid token type',
        code: 'INVALID_TOKEN_TYPE'
      });
    }

    const user = await User.findById(decoded.id);
    
    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'User not found or inactive',
        code: 'USER_NOT_FOUND'
      });
    }

    // Check if refresh token exists and is valid
    const tokenIndex = user.refreshTokens.findIndex(token => 
      token.token === refreshToken && token.expiresAt > new Date()
    );

    if (tokenIndex === -1) {
      return res.status(401).json({
        success: false,
        message: 'Invalid or expired refresh token',
        code: 'INVALID_REFRESH_TOKEN'
      });
    }

    // Generate new access token
    const newAccessToken = user.generateAccessToken();

    logger.info('Access token refreshed', {
      userId: user._id,
      email: user.email
    });

    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        accessToken: newAccessToken,
        expiresIn: process.env.JWT_EXPIRES_IN || '15m'
      }
    });
  } catch (error) {
    logger.error('Token refresh failed', { error: error.message });
    
    return res.status(401).json({
      success: false,
      message: 'Invalid refresh token',
      code: 'INVALID_REFRESH_TOKEN'
    });
  }
}));

// POST /api/auth/logout - User logout
router.post('/logout', authenticate, catchAsync(async (req, res) => {
  const { refreshToken } = req.body;
  const user = req.user;

  if (refreshToken) {
    // Remove specific refresh token
    user.refreshTokens = user.refreshTokens.filter(token => token.token !== refreshToken);
  } else {
    // Remove all refresh tokens (logout from all devices)
    user.refreshTokens = [];
  }

  await user.save();

  logger.info('User logged out', {
    userId: user._id,
    email: user.email
  });

  res.json({
    success: true,
    message: 'Logout successful'
  });
}));

// GET /api/auth/me - Get current user
router.get('/me', authenticate, catchAsync(async (req, res) => {
  res.json({
    success: true,
    data: req.user
  });
}));

// PUT /api/auth/profile - Update user profile
router.put('/profile', authenticate, [
  body('firstName').optional().trim().isLength({ min: 2, max: 50 }),
  body('lastName').optional().trim().isLength({ min: 2, max: 50 }),
  body('phone').optional().matches(/^[\+]?[1-9][\d]{0,15}$/)
], validateRequest, catchAsync(async (req, res) => {
  const { firstName, lastName, phone, preferences } = req.body;
  const user = req.user;

  if (firstName) user.firstName = firstName;
  if (lastName) user.lastName = lastName;
  if (phone) user.phone = phone;
  if (preferences) user.preferences = { ...user.preferences, ...preferences };

  await user.save();

  logger.info('User profile updated', {
    userId: user._id,
    email: user.email
  });

  res.json({
    success: true,
    message: 'Profile updated successfully',
    data: user
  });
}));

module.exports = router;
