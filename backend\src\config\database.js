const mongoose = require('mongoose');
const logger = require('./logger');

class DatabaseConnection {
  constructor() {
    this.isConnected = false;
    this.connectionAttempts = 0;
    this.maxRetries = 5;
    this.retryDelay = 5000;
  }

  async connect() {
    try {
      const mongoUri = process.env.NODE_ENV === 'test' 
        ? process.env.MONGODB_TEST_URI 
        : process.env.MONGODB_URI;

      if (!mongoUri) {
        throw new Error('MongoDB URI is not defined in environment variables');
      }

      const options = {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        family: 4,
        retryWrites: true,
        w: 'majority',
        readPreference: 'primary',
        bufferCommands: false,
        bufferMaxEntries: 0,
      };

      // Add authentication if credentials are provided
      if (process.env.MONGODB_USER && process.env.MONGODB_PASSWORD) {
        options.auth = {
          username: process.env.MONGODB_USER,
          password: process.env.MONGODB_PASSWORD,
        };
      }

      await mongoose.connect(mongoUri, options);
      
      this.isConnected = true;
      this.connectionAttempts = 0;
      
      logger.info('✅ Successfully connected to MongoDB', {
        database: mongoose.connection.name,
        host: mongoose.connection.host,
        port: mongoose.connection.port,
        environment: process.env.NODE_ENV
      });

      this.setupEventListeners();
      
      return mongoose.connection;
    } catch (error) {
      this.connectionAttempts++;
      logger.error('❌ Failed to connect to MongoDB', {
        error: error.message,
        attempt: this.connectionAttempts,
        maxRetries: this.maxRetries
      });

      if (this.connectionAttempts < this.maxRetries) {
        logger.info(`🔄 Retrying connection in ${this.retryDelay / 1000} seconds...`);
        await this.delay(this.retryDelay);
        return this.connect();
      } else {
        logger.error('💥 Max connection attempts reached. Exiting...');
        process.exit(1);
      }
    }
  }

  setupEventListeners() {
    mongoose.connection.on('connected', () => {
      logger.info('🔗 Mongoose connected to MongoDB');
      this.isConnected = true;
    });

    mongoose.connection.on('error', (error) => {
      logger.error('🚨 Mongoose connection error:', { error: error.message });
      this.isConnected = false;
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('🔌 Mongoose disconnected from MongoDB');
      this.isConnected = false;
    });

    // Handle application termination
    process.on('SIGINT', this.gracefulShutdown.bind(this));
    process.on('SIGTERM', this.gracefulShutdown.bind(this));
  }

  async gracefulShutdown() {
    try {
      logger.info('🛑 Received shutdown signal. Closing MongoDB connection...');
      await mongoose.connection.close();
      logger.info('✅ MongoDB connection closed successfully');
      process.exit(0);
    } catch (error) {
      logger.error('❌ Error during MongoDB shutdown:', { error: error.message });
      process.exit(1);
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      name: mongoose.connection.name
    };
  }

  async disconnect() {
    if (this.isConnected) {
      await mongoose.connection.close();
      this.isConnected = false;
      logger.info('🔌 Manually disconnected from MongoDB');
    }
  }
}

module.exports = new DatabaseConnection();
